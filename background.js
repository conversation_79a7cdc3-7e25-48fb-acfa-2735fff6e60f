(function () {
    chrome.runtime.onInstalled.addListener((function (e) {
        "update" !== e.reason && "install" !== e.reason || chrome.storage.local.get(["version"], (function (e) {
            const t = e.version,
                n = chrome.runtime.getManifest().version;
            if (!t) return void chrome.storage.local.set({
                version: n
            }, (function () {
                chrome.tabs.create({
                    url: "whats-new.html"
                })
            }));
            const o = t.split(".").map((e => parseInt(e, 10))),
                r = n.split(".").map((e => parseInt(e, 10)));
            (r[0] > o[0] || r[0] === o[0] && r[1] > o[1]) && chrome.tabs.create({
                url: "whats-new.html"
            }), chrome.storage.local.set({
                version: n
            })
        }))
    }))
})();