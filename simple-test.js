// VERY SIMPLE TEST SCRIPT
console.log("🚨 SIMPLE-TEST.JS LOADED!");
alert("SIMPLE TEST SCRIPT IS RUNNING!");

// Test when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log("🚨 SIMPLE TEST - DOM READY!");
    alert("SIMPLE TEST - DOM READY!");
    
    // Add click listeners to ALL buttons
    setTimeout(function() {
        console.log("🚨 ADDING BUTTON LISTENERS...");
        
        const allButtons = document.querySelectorAll('button');
        console.log("Found buttons:", allButtons.length);
        
        allButtons.forEach((btn, index) => {
            console.log(`Button ${index}:`, btn.textContent.trim());
            
            btn.addEventListener('click', function(e) {
                console.log(`🚨 BUTTON ${index} CLICKED!`);
                alert(`BUTTON CLICKED: ${btn.textContent.trim()}`);
            }, false);
        });
        
        console.log("🚨 ALL BUTTONS HAVE LISTENERS");
    }, 1000);
});
