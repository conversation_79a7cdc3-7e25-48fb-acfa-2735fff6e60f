// WORKING SCRIPT - No console logging, no DOM events
alert("SIMPLE TEST SCRIPT IS RUNNING!");

// Use setTimeout instead of DOM events
setTimeout(function() {
    alert("TIMEOUT REACHED - LOOKING FOR BUTTONS");

    // Find the Find best settings button specifically
    const searchBtn = document.getElementById('searchBtn');
    if (searchBtn) {
        alert("FOUND SEARCH BUTTON!");

        // Add click listener that tries to work with Vue
        searchBtn.addEventListener('click', function(e) {
            alert("SEARCH BUTTON CLICKED!");

            // First, let the original Vue handler run by not preventing default
            // We'll check for Vue methods after a delay

            // Try multiple approaches to find Vue changePair method
            try {
                let found = false;

                // Method 1: Check app element for Vue 3 app
                const appEl = document.getElementById('app');
                if (appEl && appEl.__vue_app__) {
                    alert("FOUND VUE APP INSTANCE");

                    // Try to get component through container
                    const container = appEl.__vue_app__._container;
                    if (container && container._vnode && container._vnode.component) {
                        const comp = container._vnode.component;
                        if (comp.ctx && comp.ctx.changePair) {
                            alert("FOUND CHANGEPAIR IN ROOT COMPONENT!");
                            comp.ctx.changePair();
                            found = true;
                        } else {
                            alert("ROOT COMPONENT HAS NO CHANGEPAIR");
                        }
                    }
                }

                // Method 2: Search all elements for Vue components
                if (!found) {
                    alert("SEARCHING ALL ELEMENTS FOR VUE...");
                    const allElements = document.querySelectorAll('*');
                    let vueElementsFound = 0;

                    for (let el of allElements) {
                        if (el.__vueParentComponent) {
                            vueElementsFound++;
                            const ctx = el.__vueParentComponent.ctx;
                            if (ctx && ctx.changePair) {
                                alert("FOUND CHANGEPAIR IN ELEMENT!");
                                ctx.changePair();
                                found = true;
                                break;
                            }
                        }
                    }

                    alert("FOUND " + vueElementsFound + " VUE ELEMENTS");
                }

                // Method 3: Try to trigger original button click
                if (!found) {
                    alert("TRYING ORIGINAL BUTTON APPROACH...");
                    // Don't prevent default, let Vue handle the click
                    // This might work if Vue is listening
                    found = true; // Let it pass through
                    return; // Don't prevent the original click
                }

                if (!found) {
                    alert("ALL VUE SEARCH METHODS FAILED");
                }
            } catch (error) {
                alert("ERROR: " + error.message);
            }

            // Give Vue a chance to handle the click first
            setTimeout(function() {
                alert("CHECKING IF VUE HANDLED THE CLICK...");
                // If we get here and nothing happened, Vue didn't handle it
            }, 1000);

        }, false);

        alert("SEARCH BUTTON LISTENER ADDED!");
    } else {
        alert("SEARCH BUTTON NOT FOUND");
    }

    // Also add listeners to all buttons for testing
    const allButtons = document.querySelectorAll('button');
    alert("FOUND " + allButtons.length + " BUTTONS TOTAL");

    allButtons.forEach((btn, index) => {
        btn.addEventListener('click', function(e) {
            alert(`BUTTON ${index} CLICKED: ${btn.textContent.trim()}`);
        }, false);
    });

}, 2000); // Wait 2 seconds for Vue to load
