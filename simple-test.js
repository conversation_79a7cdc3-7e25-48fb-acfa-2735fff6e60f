// WORKING SCRIPT - No console logging, no DOM events
alert("SIMPLE TEST SCRIPT IS RUNNING!");

// Use setTimeout instead of DOM events
setTimeout(function() {
    alert("TIMEOUT REACHED - LOOKING FOR BUTTONS");

    // Find the Find best settings button specifically
    const searchBtn = document.getElementById('searchBtn');
    if (searchBtn) {
        alert("FOUND SEARCH BUTTON!");

        // Add click listener that calls Vue method directly
        searchBtn.addEventListener('click', function(e) {
            alert("SEARCH BUTTON CLICKED!");

            // Try to find and call Vue changePair method
            try {
                // Look for Vue instance
                const allElements = document.querySelectorAll('*');
                let found = false;

                for (let el of allElements) {
                    if (el.__vueParentComponent && el.__vueParentComponent.ctx && el.__vueParentComponent.ctx.changePair) {
                        alert("FOUND VUE CHANGEPAIR - CALLING IT!");
                        el.__vueParentComponent.ctx.changePair();
                        found = true;
                        break;
                    }
                }

                if (!found) {
                    alert("VUE CHANGEPAIR NOT FOUND");
                }
            } catch (error) {
                alert("ERROR: " + error.message);
            }
        }, false);

        alert("SEARCH BUTTON LISTENER ADDED!");
    } else {
        alert("SEARCH BUTTON NOT FOUND");
    }

    // Also add listeners to all buttons for testing
    const allButtons = document.querySelectorAll('button');
    alert("FOUND " + allButtons.length + " BUTTONS TOTAL");

    allButtons.forEach((btn, index) => {
        btn.addEventListener('click', function(e) {
            alert(`BUTTON ${index} CLICKED: ${btn.textContent.trim()}`);
        }, false);
    });

}, 2000); // Wait 2 seconds for Vue to load
