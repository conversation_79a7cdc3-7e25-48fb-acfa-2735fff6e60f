<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Extension</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            width: 400px;
            height: 600px;
            margin: 0;
            padding: 10px;
            font-family: Arial, sans-serif;
        }
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-size: 12px;
        }
        .btn {
            margin: 5px 0;
        }
        .field-row {
            border: 1px solid #ccc;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }
        .input-group {
            margin: 5px 0;
        }
        .input-group input {
            margin: 2px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .success {
            color: green;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h3>Debug Extension</h3>
    
    <div class="debug-info">
        <strong>Debug Info:</strong>
        <div id="debug-output">Ready for testing...</div>
    </div>

    <div>
        <label>Pairs:</label>
        <input type="text" id="pairs" placeholder="BTCUSD, ETHUSD" class="form-control">
    </div>

    <div>
        <label>Timeframes:</label>
        <input type="text" id="timeframes" placeholder="1h, 4h" class="form-control">
    </div>

    <div id="fields-container">
        <div class="field-row" data-field="0">
            <h5>Parameter 1</h5>
            <div class="input-group">
                <span>Field #:</span>
                <input type="text" class="field-no" placeholder="1">
                <span>Step Size:</span>
                <input type="text" class="step-size" placeholder="1">
            </div>
            <div class="input-group">
                <span>From:</span>
                <input type="text" class="from" placeholder="10">
                <span>To:</span>
                <input type="text" class="to" placeholder="20">
            </div>
        </div>
    </div>

    <div>
        <button id="add-field-btn" class="btn btn-secondary">
            <i class="fas fa-plus"></i> Add Parameter
        </button>
        <button id="remove-field-btn" class="btn btn-danger" style="display: none;">
            <i class="fas fa-trash"></i> Remove Parameter
        </button>
    </div>

    <div>
        <button id="find-settings-btn" class="btn btn-primary">
            <i class="fas fa-search"></i> Find Best Settings
        </button>
    </div>

    <div id="error-display" class="error" style="display: none;"></div>
    <div id="success-display" class="success" style="display: none;"></div>

    <script>
        let fieldCount = 1;
        
        function log(message) {
            const debugOutput = document.getElementById('debug-output');
            const timestamp = new Date().toLocaleTimeString();
            debugOutput.innerHTML += `<br>[${timestamp}] ${message}`;
            debugOutput.scrollTop = debugOutput.scrollHeight;
            console.log(`[DEBUG] ${message}`);
        }

        function showError(message) {
            const errorDiv = document.getElementById('error-display');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            document.getElementById('success-display').style.display = 'none';
            log(`ERROR: ${message}`);
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('success-display');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            document.getElementById('error-display').style.display = 'none';
            log(`SUCCESS: ${message}`);
        }

        function addField() {
            log(`Adding field ${fieldCount + 1}`);
            
            const container = document.getElementById('fields-container');
            const newField = document.createElement('div');
            newField.className = 'field-row';
            newField.setAttribute('data-field', fieldCount);
            
            newField.innerHTML = `
                <h5>Parameter ${fieldCount + 1}</h5>
                <div class="input-group">
                    <span>Field #:</span>
                    <input type="text" class="field-no" placeholder="${fieldCount + 1}">
                    <span>Step Size:</span>
                    <input type="text" class="step-size" placeholder="1">
                </div>
                <div class="input-group">
                    <span>From:</span>
                    <input type="text" class="from" placeholder="10">
                    <span>To:</span>
                    <input type="text" class="to" placeholder="20">
                </div>
            `;
            
            container.appendChild(newField);
            fieldCount++;
            
            // Show remove button if more than 1 field
            if (fieldCount > 1) {
                document.getElementById('remove-field-btn').style.display = 'inline-block';
            }
            
            log(`Field added successfully. Total fields: ${fieldCount}`);
        }

        function removeField() {
            if (fieldCount <= 1) {
                log('Cannot remove last field');
                return;
            }
            
            log(`Removing field ${fieldCount}`);
            
            const container = document.getElementById('fields-container');
            const lastField = container.querySelector(`[data-field="${fieldCount - 1}"]`);
            if (lastField) {
                container.removeChild(lastField);
                fieldCount--;
                log(`Field removed successfully. Total fields: ${fieldCount}`);
            }
            
            // Hide remove button if only 1 field left
            if (fieldCount <= 1) {
                document.getElementById('remove-field-btn').style.display = 'none';
            }
        }

        function validateFields() {
            const pairs = document.getElementById('pairs').value.trim();
            const timeframes = document.getElementById('timeframes').value.trim();
            
            if (!pairs) {
                throw new Error('Please enter at least one trading pair');
            }
            
            if (!timeframes) {
                throw new Error('Please enter at least one timeframe');
            }
            
            // Validate each field
            const fieldRows = document.querySelectorAll('.field-row');
            for (let i = 0; i < fieldRows.length; i++) {
                const row = fieldRows[i];
                const fieldNo = row.querySelector('.field-no').value.trim();
                const stepSize = row.querySelector('.step-size').value.trim();
                const from = row.querySelector('.from').value.trim();
                const to = row.querySelector('.to').value.trim();
                
                if (!fieldNo || !stepSize || !from || !to) {
                    throw new Error(`Please fill all fields for Parameter ${i + 1}`);
                }
                
                if (isNaN(fieldNo) || isNaN(stepSize) || isNaN(from) || isNaN(to)) {
                    throw new Error(`All values for Parameter ${i + 1} must be numbers`);
                }
                
                if (parseFloat(from) >= parseFloat(to)) {
                    throw new Error(`For Parameter ${i + 1}, 'From' value must be less than 'To' value`);
                }
            }
            
            return true;
        }

        function findBestSettings() {
            log('Find Best Settings button clicked');
            
            try {
                validateFields();
                showSuccess('Validation passed! In real extension, this would start the backtesting process.');
                log('All validations passed - ready to start backtesting');
                
                // Simulate what the real extension would do
                const pairs = document.getElementById('pairs').value.split(',').map(p => p.trim());
                const timeframes = document.getElementById('timeframes').value.split(',').map(t => t.trim());
                const fieldRows = document.querySelectorAll('.field-row');
                
                log(`Pairs: ${pairs.join(', ')}`);
                log(`Timeframes: ${timeframes.join(', ')}`);
                log(`Parameters: ${fieldRows.length}`);
                
                // Calculate total steps
                let totalSteps = 1;
                fieldRows.forEach((row, index) => {
                    const from = parseFloat(row.querySelector('.from').value);
                    const to = parseFloat(row.querySelector('.to').value);
                    const stepSize = parseFloat(row.querySelector('.step-size').value);
                    const steps = Math.floor((to - from) / stepSize) + 1;
                    totalSteps *= steps;
                    log(`Parameter ${index + 1}: ${steps} steps`);
                });
                
                totalSteps *= pairs.length * timeframes.length;
                log(`Total steps to execute: ${totalSteps.toLocaleString()}`);
                
            } catch (error) {
                showError(error.message);
            }
        }

        // Event listeners
        document.getElementById('add-field-btn').addEventListener('click', addField);
        document.getElementById('remove-field-btn').addEventListener('click', removeField);
        document.getElementById('find-settings-btn').addEventListener('click', findBestSettings);

        // Test Chrome extension APIs
        log('Testing Chrome extension environment...');
        if (typeof chrome !== 'undefined' && chrome.tabs) {
            log('Chrome extension APIs available');
        } else {
            log('Chrome extension APIs NOT available (normal in browser)');
        }

        log('Debug extension loaded successfully');
    </script>
</body>
</html>
