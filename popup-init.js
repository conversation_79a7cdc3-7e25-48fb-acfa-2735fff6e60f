// External popup initialization script
console.log("🔥 POPUP-INIT.JS LOADED");
console.log("Document ready state:", document.readyState);

// Catch all JavaScript errors
window.addEventListener('error', function(e) {
    console.error("❌ JAVASCRIPT ERROR:", e.error);
    console.error("Error message:", e.message);
    console.error("Error filename:", e.filename);
    console.error("Error line:", e.lineno);
    
    // Show error in popup if V<PERSON> fails
    const appElement = document.getElementById('app');
    if (appElement && !appElement.innerHTML.trim()) {
        appElement.innerHTML = `
            <div style="padding: 20px; color: red; font-family: Arial;">
                <h3>⚠️ Extension Error</h3>
                <p><strong>Error:</strong> ${e.message}</p>
                <p><strong>File:</strong> ${e.filename}</p>
                <p><strong>Line:</strong> ${e.lineno}</p>
                <p>Check the console for more details.</p>
            </div>
        `;
    }
});

// Test when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log("📄 DOM Content Loaded");
    const appElement = document.getElementById('app');
    console.log("App element found:", !!appElement);

    // Continuously monitor for the button and add listeners
    let buttonCheckCount = 0;
    const buttonChecker = setInterval(function() {
        buttonCheckCount++;
        console.log(`🔍 Button check #${buttonCheckCount}`);

        const searchBtn = document.getElementById('searchBtn');
        const allButtons = document.querySelectorAll('button');

        console.log(`Found ${allButtons.length} buttons total`);

        if (searchBtn) {
            console.log("✅ Search button found!");
            console.log("Button text:", searchBtn.textContent.trim());
            console.log("Button disabled:", searchBtn.disabled);
            console.log("Button style display:", searchBtn.style.display);
            console.log("Button computed style:", window.getComputedStyle(searchBtn).display);

            // Check if Vue.js click handler is attached
            console.log("Button onclick property:", searchBtn.onclick);
            console.log("Button __vueParentComponent:", searchBtn.__vueParentComponent);

            // Force button to be properly clickable
            searchBtn.style.pointerEvents = 'auto';
            searchBtn.style.cursor = 'pointer';

            // Remove any existing click handlers and add our own
            const newSearchBtn = searchBtn.cloneNode(true);
            searchBtn.parentNode.replaceChild(newSearchBtn, searchBtn);

            newSearchBtn.addEventListener('click', function(e) {
                console.log("🔥 FORCED SEARCH BUTTON CLICK!");
                e.preventDefault();
                e.stopPropagation();

                // Show immediate visual feedback
                newSearchBtn.style.backgroundColor = '#28a745';
                newSearchBtn.textContent = 'Starting...';

                // Find Vue instance and call method
                setTimeout(() => {
                    try {
                        // Try to find Vue instance through multiple methods
                        let vueInstance = null;

                        // Method 1: Check app element for Vue 3 app instance
                        const appEl = document.getElementById('app');
                        if (appEl && appEl.__vue_app__) {
                            console.log("Found Vue 3 app instance");
                            console.log("App properties:", Object.keys(appEl.__vue_app__));

                            // Try multiple ways to get the component instance
                            let rootComponent = appEl.__vue_app__._instance;

                            // If _instance is null, try _container._vnode.component
                            if (!rootComponent && appEl.__vue_app__._container) {
                                const container = appEl.__vue_app__._container;
                                console.log("Checking container:", container);
                                if (container._vnode && container._vnode.component) {
                                    rootComponent = container._vnode.component;
                                    console.log("Found component through _vnode");
                                }
                            }

                            // Try to find component through the app element itself
                            if (!rootComponent && appEl.__vueParentComponent) {
                                rootComponent = appEl.__vueParentComponent;
                                console.log("Found component through __vueParentComponent");
                            }

                            if (rootComponent && rootComponent.ctx && rootComponent.ctx.changePair) {
                                vueInstance = rootComponent.ctx;
                                console.log("Found changePair in root component");
                            } else if (rootComponent) {
                                console.log("Root component found but no changePair:", Object.keys(rootComponent.ctx || {}));

                                // Search deeper into the component tree
                                console.log("🔍 Searching component tree for changePair...");
                                const searchComponentTree = (component, depth = 0) => {
                                    if (!component) return null;

                                    const indent = "  ".repeat(depth);
                                    console.log(`${indent}Component at depth ${depth}:`, component.type?.name || 'Anonymous');

                                    // Check current component context
                                    if (component.ctx && component.ctx.changePair) {
                                        console.log(`${indent}✅ Found changePair at depth ${depth}!`);
                                        return component.ctx;
                                    }

                                    // Check subTree (child components)
                                    if (component.subTree) {
                                        if (component.subTree.component) {
                                            const result = searchComponentTree(component.subTree.component, depth + 1);
                                            if (result) return result;
                                        }

                                        // Check children in subTree
                                        if (component.subTree.children && Array.isArray(component.subTree.children)) {
                                            for (const child of component.subTree.children) {
                                                if (child.component) {
                                                    const result = searchComponentTree(child.component, depth + 1);
                                                    if (result) return result;
                                                }
                                            }
                                        }
                                    }

                                    return null;
                                };

                                const foundInstance = searchComponentTree(rootComponent);
                                if (foundInstance) {
                                    vueInstance = foundInstance;
                                    console.log("✅ Found changePair in component tree!");
                                }
                            }
                        }

                        // Method 2: Check all elements for Vue component instances
                        if (!vueInstance) {
                            const allElements = document.querySelectorAll('*');
                            for (let el of allElements) {
                                // Vue 3 component instance
                                if (el.__vueParentComponent && el.__vueParentComponent.ctx) {
                                    const ctx = el.__vueParentComponent.ctx;
                                    if (ctx.changePair && typeof ctx.changePair === 'function') {
                                        vueInstance = ctx;
                                        console.log("Found changePair in component context");
                                        break;
                                    }
                                }
                                // Vue 2 style (fallback)
                                if (el.__vue__ && el.__vue__.changePair) {
                                    vueInstance = el.__vue__;
                                    console.log("Found changePair in Vue 2 style instance");
                                    break;
                                }
                            }
                        }

                        if (vueInstance) {
                            console.log("✅ Found Vue instance, calling changePair!");
                            console.log("Vue instance methods:", Object.keys(vueInstance).filter(key => typeof vueInstance[key] === 'function'));
                            vueInstance.changePair();
                        } else {
                            console.log("❌ Could not find Vue instance");
                            console.log("App element:", appEl);
                            console.log("App element __vue_app__:", appEl ? appEl.__vue_app__ : 'No app element');
                            console.log("App instance:", appEl && appEl.__vue_app__ ? appEl.__vue_app__._instance : 'No instance');

                            // Try one more desperate attempt - comprehensive Vue instance search
                            let foundAny = false;
                            console.log("🔍 Comprehensive Vue instance search...");

                            document.querySelectorAll('*').forEach((el, index) => {
                                // Check for various Vue 3 properties
                                const vueProps = [];
                                if (el.__vueParentComponent) vueProps.push('__vueParentComponent');
                                if (el.__vue__) vueProps.push('__vue__');
                                if (el._vnode) vueProps.push('_vnode');
                                if (el.__vnode) vueProps.push('__vnode');

                                if (vueProps.length > 0) {
                                    console.log(`Element ${index} (${el.tagName}):`, vueProps);

                                    // Check __vueParentComponent
                                    if (el.__vueParentComponent && el.__vueParentComponent.ctx) {
                                        const ctx = el.__vueParentComponent.ctx;
                                        const methods = Object.keys(ctx).filter(key => typeof ctx[key] === 'function');
                                        console.log(`  Methods in ctx:`, methods);

                                        // Look for changePair or similar methods
                                        if (ctx.changePair && typeof ctx.changePair === 'function') {
                                            console.log("🎯 FOUND changePair in:", el.tagName, el.className);
                                            foundAny = true;
                                            try {
                                                ctx.changePair();
                                                console.log("✅ Successfully called changePair!");
                                                return; // Exit the forEach
                                            } catch (e) {
                                                console.error("❌ Error calling changePair:", e);
                                            }
                                        }

                                        // Also check for components with many methods (likely the Main component)
                                        if (methods.length > 5) {
                                            console.log(`  🎯 Found component with ${methods.length} methods:`, el.tagName, el.id, el.className);
                                            console.log(`  All methods:`, methods);

                                            // Try to find changePair or similar
                                            const changePairMethod = methods.find(m => m.toLowerCase().includes('changepair') || m.toLowerCase().includes('change_pair'));
                                            if (changePairMethod) {
                                                console.log(`  🎯 Found similar method: ${changePairMethod}`);
                                                try {
                                                    ctx[changePairMethod]();
                                                    console.log("✅ Successfully called", changePairMethod);
                                                    foundAny = true;
                                                    return;
                                                } catch (e) {
                                                    console.error("❌ Error calling", changePairMethod, e);
                                                }
                                            }
                                        }
                                    }

                                    // Check __vue__ (Vue 2 style)
                                    if (el.__vue__ && el.__vue__.changePair) {
                                        console.log("🎯 FOUND changePair in Vue 2 style:", el.tagName);
                                        foundAny = true;
                                        try {
                                            el.__vue__.changePair();
                                            console.log("✅ Successfully called changePair!");
                                            return;
                                        } catch (e) {
                                            console.error("❌ Error calling changePair:", e);
                                        }
                                    }
                                }
                            });

                            if (!foundAny) {
                                alert("Extension error: Could not find Vue instance with changePair method. Check console for details.");
                            }
                        }
                    } catch (error) {
                        console.error("❌ Error:", error);
                        alert("Extension error: " + error.message);
                    }

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        newSearchBtn.style.backgroundColor = '';
                        newSearchBtn.textContent = 'Find best settings';
                    }, 2000);
                }, 100);
            }, true);


            // Stop checking once we've set up the button
            clearInterval(buttonChecker);
            console.log("✅ Forced button handler attached, stopping checker");
        } else {
            console.log("❌ Search button not found");
        }

        // Also look for and fix the + button (add field button)
        const addButtons = document.querySelectorAll('button, .btn, [role="button"], i.fa-plus, .fa-plus');
        console.log(`Checking ${addButtons.length} elements for + button`);

        addButtons.forEach((btn, index) => {
            const text = btn.textContent?.trim() || '';
            const classes = btn.className || '';

            if (text.includes('+') || classes.includes('fa-plus') || classes.includes('add') || btn.title?.includes('add')) {
                console.log(`Found potential + button ${index}:`, text, classes);

                // Force this button to be clickable too
                btn.style.pointerEvents = 'auto';
                btn.style.cursor = 'pointer';

                // Clone and replace to remove existing handlers
                const newAddBtn = btn.cloneNode(true);
                btn.parentNode.replaceChild(newAddBtn, btn);

                newAddBtn.addEventListener('click', function(e) {
                    console.log("🔥 FORCED + BUTTON CLICK!");
                    e.preventDefault();
                    e.stopPropagation();

                    // Show visual feedback
                    newAddBtn.style.backgroundColor = '#007bff';

                    // Try to find and call the Vue add method
                    setTimeout(() => {
                        try {
                            let vueInstance = null;
                            const allElements = document.querySelectorAll('*');
                            for (let el of allElements) {
                                if (el.__vueParentComponent && el.__vueParentComponent.ctx) {
                                    const ctx = el.__vueParentComponent.ctx;
                                    if (ctx.addField && typeof ctx.addField === 'function') {
                                        vueInstance = ctx;
                                        break;
                                    }
                                }
                            }

                            if (vueInstance && vueInstance.addField) {
                                console.log("✅ Found Vue instance, calling addField!");
                                vueInstance.addField();
                            } else {
                                console.log("❌ Could not find addField method");
                                alert("Add field error: Could not find Vue addField method.");
                            }
                        } catch (error) {
                            console.error("❌ Add field error:", error);
                            alert("Add field error: " + error.message);
                        }

                        // Reset button
                        setTimeout(() => {
                            newAddBtn.style.backgroundColor = '';
                        }, 1000);
                    }, 100);
                }, true);
            }
        });

        // Stop after 10 attempts
        if (buttonCheckCount >= 10) {
            clearInterval(buttonChecker);
            console.log("⏰ Stopped checking after 10 attempts");
        }

        if (appElement && !appElement.innerHTML.trim()) {
            console.warn("⚠️ Vue app didn't load - showing fallback");
            appElement.innerHTML = `
                <div style="padding: 20px; font-family: Arial;">
                    <h3>🔧 Extension Loading...</h3>
                    <p>If this message persists, check the console for errors.</p>
                    <button id="reload-btn">Reload Extension</button>
                </div>
            `;

            // Add event listener for reload button (no inline onclick)
            const reloadBtn = document.getElementById('reload-btn');
            if (reloadBtn) {
                reloadBtn.addEventListener('click', function() {
                    location.reload();
                });
            }
        }
    }, 3000);
});

// Test when everything is loaded
window.addEventListener('load', function() {
    console.log("🌐 Window Load Complete");
});

// Add global click detector to catch ANY click
document.addEventListener('click', function(e) {
    console.log("🌍 GLOBAL CLICK DETECTED!");
    console.log("Target:", e.target);
    console.log("Target tag:", e.target.tagName);
    console.log("Target text:", e.target.textContent?.trim());
    console.log("Target classes:", e.target.className);
    console.log("Target ID:", e.target.id);
}, true);

// Also add mousedown detector
document.addEventListener('mousedown', function(e) {
    console.log("🖱️ GLOBAL MOUSEDOWN DETECTED!");
    console.log("Target:", e.target.tagName, e.target.textContent?.trim());
}, true);
