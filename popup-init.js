// External popup initialization script
console.log("🔥 POPUP-INIT.JS LOADED");
console.log("Document ready state:", document.readyState);

// Catch all JavaScript errors
window.addEventListener('error', function(e) {
    console.error("❌ JAVASCRIPT ERROR:", e.error);
    console.error("Error message:", e.message);
    console.error("Error filename:", e.filename);
    console.error("Error line:", e.lineno);
    
    // Show error in popup if V<PERSON> fails
    const appElement = document.getElementById('app');
    if (appElement && !appElement.innerHTML.trim()) {
        appElement.innerHTML = `
            <div style="padding: 20px; color: red; font-family: Arial;">
                <h3>⚠️ Extension Error</h3>
                <p><strong>Error:</strong> ${e.message}</p>
                <p><strong>File:</strong> ${e.filename}</p>
                <p><strong>Line:</strong> ${e.lineno}</p>
                <p>Check the console for more details.</p>
            </div>
        `;
    }
});

// Test when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log("📄 DOM Content Loaded");
    const appElement = document.getElementById('app');
    console.log("App element found:", !!appElement);

    // Continuously monitor for the button and add listeners
    let buttonCheckCount = 0;
    const buttonChecker = setInterval(function() {
        buttonCheckCount++;
        console.log(`🔍 Button check #${buttonCheckCount}`);

        const searchBtn = document.getElementById('searchBtn');
        const allButtons = document.querySelectorAll('button');

        console.log(`Found ${allButtons.length} buttons total`);

        if (searchBtn) {
            console.log("✅ Search button found!");
            console.log("Button text:", searchBtn.textContent.trim());
            console.log("Button disabled:", searchBtn.disabled);
            console.log("Button style display:", searchBtn.style.display);
            console.log("Button computed style:", window.getComputedStyle(searchBtn).display);

            // Check if Vue.js click handler is attached
            console.log("Button onclick property:", searchBtn.onclick);
            console.log("Button __vueParentComponent:", searchBtn.__vueParentComponent);

            // Force button to be properly clickable
            searchBtn.style.pointerEvents = 'auto';
            searchBtn.style.cursor = 'pointer';

            // Don't replace the button - just add a simple click interceptor
            console.log("Adding simple click interceptor...");

            // Add click handler that works with Vue
            searchBtn.addEventListener('click', function(e) {
                console.log("🔥 SIMPLE CLICK INTERCEPTOR!");

                // Don't prevent default - let Vue handle it normally
                // Just add visual feedback
                searchBtn.style.backgroundColor = '#28a745';
                searchBtn.textContent = 'Processing...';

                // Reset after 3 seconds
                setTimeout(() => {
                    searchBtn.style.backgroundColor = '';
                    searchBtn.textContent = 'Find best settings';
                }, 3000);

                console.log("Click passed to Vue.js...");
            }, false); // Use bubbling phase, don't capture

            // Also try direct method call as backup
            setTimeout(() => {
                console.log("🔍 Backup: Looking for Vue methods...");
                try {
                    try {
                        // Simple Vue instance search
                        let vueInstance = null;
                        console.log("🔍 Simple Vue search...");

                        // Just look for any element with changePair method
                        const allElements = document.querySelectorAll('*');
                        for (let el of allElements) {
                            if (el.__vueParentComponent && el.__vueParentComponent.ctx && el.__vueParentComponent.ctx.changePair) {
                                vueInstance = el.__vueParentComponent.ctx;
                                console.log("✅ Found changePair in element:", el.tagName);
                                break;
                            }
                        }

                        // Method 2: Check all elements for Vue component instances
                        if (!vueInstance) {
                            const allElements = document.querySelectorAll('*');
                            for (let el of allElements) {
                                // Vue 3 component instance
                                if (el.__vueParentComponent && el.__vueParentComponent.ctx) {
                                    const ctx = el.__vueParentComponent.ctx;
                                    if (ctx.changePair && typeof ctx.changePair === 'function') {
                                        vueInstance = ctx;
                                        console.log("Found changePair in component context");
                                        break;
                                    }
                                }
                                // Vue 2 style (fallback)
                                if (el.__vue__ && el.__vue__.changePair) {
                                    vueInstance = el.__vue__;
                                    console.log("Found changePair in Vue 2 style instance");
                                    break;
                                }
                            }
                        }

                        if (vueInstance) {
                            console.log("✅ Found Vue instance, calling changePair!");
                            vueInstance.changePair();
                        } else {
                            console.log("❌ Could not find Vue instance with changePair");
                        }
                } catch (error) {
                    console.error("❌ Error in Vue search:", error);
                }
            }, 1000); // Run backup search after 1 second


            // Stop checking once we've set up the button
            clearInterval(buttonChecker);
            console.log("✅ Forced button handler attached, stopping checker");
        } else {
            console.log("❌ Search button not found");
        }

        // Also look for and fix the + button (add field button)
        const addButtons = document.querySelectorAll('button, .btn, [role="button"], i.fa-plus, .fa-plus');
        console.log(`Checking ${addButtons.length} elements for + button`);

        addButtons.forEach((btn, index) => {
            const text = btn.textContent?.trim() || '';
            const classes = btn.className || '';

            if (text.includes('+') || classes.includes('fa-plus') || classes.includes('add') || btn.title?.includes('add')) {
                console.log(`Found potential + button ${index}:`, text, classes);

                // Force this button to be clickable too
                btn.style.pointerEvents = 'auto';
                btn.style.cursor = 'pointer';

                // Clone and replace to remove existing handlers
                const newAddBtn = btn.cloneNode(true);
                btn.parentNode.replaceChild(newAddBtn, btn);

                newAddBtn.addEventListener('click', function(e) {
                    console.log("🔥 FORCED + BUTTON CLICK!");
                    e.preventDefault();
                    e.stopPropagation();

                    // Show visual feedback
                    newAddBtn.style.backgroundColor = '#007bff';

                    // Try to find and call the Vue add method
                    setTimeout(() => {
                        try {
                            let vueInstance = null;
                            const allElements = document.querySelectorAll('*');
                            for (let el of allElements) {
                                if (el.__vueParentComponent && el.__vueParentComponent.ctx) {
                                    const ctx = el.__vueParentComponent.ctx;
                                    if (ctx.addField && typeof ctx.addField === 'function') {
                                        vueInstance = ctx;
                                        break;
                                    }
                                }
                            }

                            if (vueInstance && vueInstance.addField) {
                                console.log("✅ Found Vue instance, calling addField!");
                                vueInstance.addField();
                            } else {
                                console.log("❌ Could not find addField method");
                                alert("Add field error: Could not find Vue addField method.");
                            }
                        } catch (error) {
                            console.error("❌ Add field error:", error);
                            alert("Add field error: " + error.message);
                        }

                        // Reset button
                        setTimeout(() => {
                            newAddBtn.style.backgroundColor = '';
                        }, 1000);
                    }, 100);
                }, true);
            }
        });

        // Stop after 10 attempts
        if (buttonCheckCount >= 10) {
            clearInterval(buttonChecker);
            console.log("⏰ Stopped checking after 10 attempts");
        }

        if (appElement && !appElement.innerHTML.trim()) {
            console.warn("⚠️ Vue app didn't load - showing fallback");
            appElement.innerHTML = `
                <div style="padding: 20px; font-family: Arial;">
                    <h3>🔧 Extension Loading...</h3>
                    <p>If this message persists, check the console for errors.</p>
                    <button id="reload-btn">Reload Extension</button>
                </div>
            `;

            // Add event listener for reload button (no inline onclick)
            const reloadBtn = document.getElementById('reload-btn');
            if (reloadBtn) {
                reloadBtn.addEventListener('click', function() {
                    location.reload();
                });
            }
        }
    }, 3000);
});

// Test when everything is loaded
window.addEventListener('load', function() {
    console.log("🌐 Window Load Complete");
});

// Add global click detector to catch ANY click
document.addEventListener('click', function(e) {
    console.log("🌍 GLOBAL CLICK DETECTED!");
    console.log("Target:", e.target);
    console.log("Target tag:", e.target.tagName);
    console.log("Target text:", e.target.textContent?.trim());
    console.log("Target classes:", e.target.className);
    console.log("Target ID:", e.target.id);
}, true);

// Also add mousedown detector
document.addEventListener('mousedown', function(e) {
    console.log("🖱️ GLOBAL MOUSEDOWN DETECTED!");
    console.log("Target:", e.target.tagName, e.target.textContent?.trim());
}, true);
