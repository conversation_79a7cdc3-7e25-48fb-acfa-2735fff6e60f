<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Popup Loading</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            border: 1px solid #ccc;
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ccc;
        }
        .console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔧 Extension Popup Test</h1>
    
    <div class="test-section">
        <h3>📋 Test Results</h3>
        <div id="test-results">
            <div class="warning">Running tests...</div>
        </div>
    </div>

    <div class="test-section">
        <h3>🖥️ Console Output</h3>
        <div class="console-output" id="console-output">Waiting for console messages...</div>
    </div>

    <div class="test-section">
        <h3>🎯 Popup Preview</h3>
        <iframe id="popup-iframe" src="popup.html"></iframe>
    </div>

    <script>
        let testResults = [];
        
        function addResult(test, status, message) {
            testResults.push({test, status, message});
            updateResults();
        }
        
        function updateResults() {
            const container = document.getElementById('test-results');
            container.innerHTML = testResults.map(result => {
                const className = result.status === 'success' ? 'success' : 
                                result.status === 'error' ? 'error' : 'warning';
                return `<div class="${className}">
                    <strong>${result.test}:</strong> ${result.message}
                </div>`;
            }).join('');
        }
        
        function addConsoleMessage(message) {
            const output = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }
        
        // Override console methods to capture messages
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            addConsoleMessage('LOG: ' + args.join(' '));
            originalLog.apply(console, arguments);
        };
        
        console.error = function(...args) {
            addConsoleMessage('ERROR: ' + args.join(' '));
            originalError.apply(console, arguments);
        };
        
        console.warn = function(...args) {
            addConsoleMessage('WARN: ' + args.join(' '));
            originalWarn.apply(console, arguments);
        };
        
        // Test 1: Check if popup.html exists and loads
        function testPopupHTML() {
            const iframe = document.getElementById('popup-iframe');
            
            iframe.onload = function() {
                try {
                    // Try to access iframe content
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    if (iframeDoc) {
                        addResult('Popup HTML Load', 'success', 'popup.html loaded successfully');
                        
                        // Check if #app element exists
                        const appElement = iframeDoc.getElementById('app');
                        if (appElement) {
                            addResult('App Element', 'success', '#app element found in popup.html');
                        } else {
                            addResult('App Element', 'error', '#app element NOT found in popup.html');
                        }
                        
                        // Check if popup.js is loaded
                        const scripts = iframeDoc.getElementsByTagName('script');
                        let popupJsFound = false;
                        for (let script of scripts) {
                            if (script.src && script.src.includes('popup.js')) {
                                popupJsFound = true;
                                break;
                            }
                        }
                        
                        if (popupJsFound) {
                            addResult('Popup JS Reference', 'success', 'popup.js script tag found');
                        } else {
                            addResult('Popup JS Reference', 'error', 'popup.js script tag NOT found');
                        }
                        
                    } else {
                        addResult('Popup HTML Load', 'error', 'Cannot access iframe content');
                    }
                } catch (e) {
                    addResult('Popup HTML Load', 'error', 'Error accessing iframe: ' + e.message);
                }
            };
            
            iframe.onerror = function() {
                addResult('Popup HTML Load', 'error', 'Failed to load popup.html');
            };
        }
        
        // Test 2: Check if files exist
        async function testFileExistence() {
            const files = ['popup.html', 'popup.js', 'manifest.json'];
            
            for (const file of files) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    if (response.ok) {
                        addResult(`File Check: ${file}`, 'success', `${file} exists and accessible`);
                    } else {
                        addResult(`File Check: ${file}`, 'error', `${file} returned ${response.status}`);
                    }
                } catch (e) {
                    addResult(`File Check: ${file}`, 'error', `${file} fetch failed: ${e.message}`);
                }
            }
        }
        
        // Test 3: Check manifest.json
        async function testManifest() {
            try {
                const response = await fetch('manifest.json');
                const manifest = await response.json();
                
                if (manifest.name && manifest.version) {
                    addResult('Manifest Validation', 'success', `Valid manifest: ${manifest.name} v${manifest.version}`);
                } else {
                    addResult('Manifest Validation', 'error', 'Invalid manifest structure');
                }
            } catch (e) {
                addResult('Manifest Validation', 'error', 'Failed to load manifest: ' + e.message);
            }
        }
        
        // Run tests
        window.onload = function() {
            addResult('Test Suite', 'success', 'Starting extension tests...');
            
            testFileExistence();
            testManifest();
            testPopupHTML();
            
            // Listen for messages from iframe
            window.addEventListener('message', function(event) {
                if (event.data && event.data.type === 'console') {
                    addConsoleMessage('IFRAME: ' + event.data.message);
                }
            });
        };
    </script>
</body>
</html>
