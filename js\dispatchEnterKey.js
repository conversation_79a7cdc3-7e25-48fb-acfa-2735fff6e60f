(function () {
    const e = document.querySelectorAll('input[class^="search"]');
    if (e.length > 0) {
        const n = 13,
            t = "keydown",
            c = new KeyboardEvent(t, {
                key: "Enter",
                keyCode: n,
                charCode: n,
                code: "Enter",
                bubbles: !0,
                cancelable: !0,
                sourceCapabilities: new InputDeviceCapabilities({
                    firesTouchEvents: !1
                })
            });
        e[0].dispatchEvent(c)
    }
})();