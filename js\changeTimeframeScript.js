(function (e) {
    const i = document.getElementsByTagName("body"),
        t = {
            0: "Digit0",
            1: "Digit1",
            2: "Digit2",
            3: "Digit3",
            4: "Digit4",
            5: "Digit5",
            6: "Digit6",
            7: "Digit7",
            8: "Digit8",
            9: "Digit9",
            s: "KeyS",
            m: "KeyM",
            h: "KeyH",
            d: "KeyD",
            w: "KeyW"
        };
    e = e.toLowerCase();
    for (let o = 0; o < e.length; o++) {
        const n = e[o],
            s = n.charCodeAt(0),
            a = "keypress",
            c = new KeyboardEvent(a, {
                key: n,
                keyCode: s,
                charCode: s,
                code: t[n],
                bubbles: !0,
                cancelable: !0,
                sourceCapabilities: new InputDeviceCapabilities({
                    firesTouchEvents: !1
                })
            });
        i[0].dispatchEvent(c)
    }
})("2");