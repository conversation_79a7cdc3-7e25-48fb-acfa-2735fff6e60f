<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Test Extension</title>
    <style>
        body {
            width: 300px;
            height: 200px;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #45a049;
        }
        #output {
            margin-top: 10px;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
            min-height: 50px;
        }
    </style>
</head>
<body>
    <h3>🧪 Extension Test</h3>
    <button class="test-button" id="test-btn">Test Button</button>
    <button class="test-button" id="add-btn">Add Item</button>
    <button class="test-button" onclick="alert('Direct onclick works!')">Direct Test</button>
    <div id="output">Click buttons to test...</div>

    <!-- Immediate test without any functions -->
    <script>
        // This should run immediately when the page loads
        document.getElementById('output').innerHTML = '<strong style="color: green;">✅ JavaScript is working!</strong><br>Script loaded at: ' + new Date().toLocaleTimeString();
    </script>

    <script>
        console.log("🔥 TEST POPUP LOADED");
        
        let itemCount = 0;
        
        function testFunction() {
            console.log("✅ Test button clicked!");
            document.getElementById('output').innerHTML = 
                `<strong>Success!</strong><br>Button clicked at ${new Date().toLocaleTimeString()}`;
        }
        
        function addItem() {
            itemCount++;
            console.log("➕ Add item clicked, count:", itemCount);
            document.getElementById('output').innerHTML = 
                `<strong>Items added:</strong> ${itemCount}<br>Last added: ${new Date().toLocaleTimeString()}`;
        }
        
        // Test that runs immediately
        document.addEventListener('DOMContentLoaded', function() {
            console.log("📄 DOM loaded in test popup");
            document.getElementById('output').innerHTML =
                '<strong>Ready!</strong><br>Extension popup loaded successfully.';

            // Add event listeners instead of inline onclick
            document.getElementById('test-btn').addEventListener('click', testFunction);
            document.getElementById('add-btn').addEventListener('click', addItem);

            console.log("🔗 Event listeners attached");
        });
    </script>
</body>
</html>
