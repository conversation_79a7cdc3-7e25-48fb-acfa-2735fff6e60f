// External JavaScript file for popup
console.log('🔥 External JS file loaded');

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 DOM ready in external JS');
    
    // Change status to show J<PERSON> is working
    const statusElement = document.getElementById('status');
    if (statusElement) {
        statusElement.textContent = 'External JavaScript Works!';
        statusElement.style.color = 'green';
    }
    
    // Add button click handler
    const button = document.getElementById('btn');
    if (button) {
        button.addEventListener('click', function() {
            console.log('🔘 Button clicked!');
            statusElement.textContent = 'Button Clicked! Time: ' + new Date().toLocaleTimeString();
            statusElement.style.color = 'blue';
        });
    }
    
    console.log('✅ Event listeners attached');
});

// Also try immediate execution
console.log('🚀 Immediate execution test');
