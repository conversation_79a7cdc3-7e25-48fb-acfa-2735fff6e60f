(function () {
    var t = {
            9306: function (t, e, r) {
                "use strict";
                var n = r(4901),
                    o = r(6823),
                    i = TypeError;
                t.exports = function (t) {
                    if (n(t)) return t;
                    throw new i(o(t) + " is not a function")
                }
            },
            3506: function (t, e, r) {
                "use strict";
                var n = r(3925),
                    o = String,
                    i = TypeError;
                t.exports = function (t) {
                    if (n(t)) return t;
                    throw new i("Can't set " + o(t) + " as a prototype")
                }
            },
            679: function (t, e, r) {
                "use strict";
                var n = r(1625),
                    o = TypeError;
                t.exports = function (t, e) {
                    if (n(e, t)) return t;
                    throw new o("Incorrect invocation")
                }
            },
            8551: function (t, e, r) {
                "use strict";
                var n = r(34),
                    o = String,
                    i = TypeError;
                t.exports = function (t) {
                    if (n(t)) return t;
                    throw new i(o(t) + " is not an object")
                }
            },
            9617: function (t, e, r) {
                "use strict";
                var n = r(5397),
                    o = r(5610),
                    i = r(6198),
                    c = function (t) {
                        return function (e, r, c) {
                            var a = n(e),
                                s = i(a);
                            if (0 === s) return !t && -1;
                            var u, l = o(c, s);
                            if (t && r !== r) {
                                while (s > l)
                                    if (u = a[l++], u !== u) return !0
                            } else
                                for (; s > l; l++)
                                    if ((t || l in a) && a[l] === r) return t || l || 0;
                            return !t && -1
                        }
                    };
                t.exports = {
                    includes: c(!0),
                    indexOf: c(!1)
                }
            },
            4527: function (t, e, r) {
                "use strict";
                var n = r(3724),
                    o = r(4376),
                    i = TypeError,
                    c = Object.getOwnPropertyDescriptor,
                    a = n && ! function () {
                        if (void 0 !== this) return !0;
                        try {
                            Object.defineProperty([], "length", {
                                writable: !1
                            }).length = 1
                        } catch (t) {
                            return t instanceof TypeError
                        }
                    }();
                t.exports = a ? function (t, e) {
                    if (o(t) && !c(t, "length").writable) throw new i("Cannot set read only .length");
                    return t.length = e
                } : function (t, e) {
                    return t.length = e
                }
            },
            2195: function (t, e, r) {
                "use strict";
                var n = r(9504),
                    o = n({}.toString),
                    i = n("".slice);
                t.exports = function (t) {
                    return i(o(t), 8, -1)
                }
            },
            6955: function (t, e, r) {
                "use strict";
                var n = r(2140),
                    o = r(4901),
                    i = r(2195),
                    c = r(8227),
                    a = c("toStringTag"),
                    s = Object,
                    u = "Arguments" === i(function () {
                        return arguments
                    }()),
                    l = function (t, e) {
                        try {
                            return t[e]
                        } catch (r) {}
                    };
                t.exports = n ? i : function (t) {
                    var e, r, n;
                    return void 0 === t ? "Undefined" : null === t ? "Null" : "string" == typeof (r = l(e = s(t), a)) ? r : u ? i(e) : "Object" === (n = i(e)) && o(e.callee) ? "Arguments" : n
                }
            },
            7740: function (t, e, r) {
                "use strict";
                var n = r(9297),
                    o = r(5031),
                    i = r(7347),
                    c = r(4913);
                t.exports = function (t, e, r) {
                    for (var a = o(e), s = c.f, u = i.f, l = 0; l < a.length; l++) {
                        var f = a[l];
                        n(t, f) || r && n(r, f) || s(t, f, u(e, f))
                    }
                }
            },
            6699: function (t, e, r) {
                "use strict";
                var n = r(3724),
                    o = r(4913),
                    i = r(6980);
                t.exports = n ? function (t, e, r) {
                    return o.f(t, e, i(1, r))
                } : function (t, e, r) {
                    return t[e] = r, t
                }
            },
            6980: function (t) {
                "use strict";
                t.exports = function (t, e) {
                    return {
                        enumerable: !(1 & t),
                        configurable: !(2 & t),
                        writable: !(4 & t),
                        value: e
                    }
                }
            },
            6840: function (t, e, r) {
                "use strict";
                var n = r(4901),
                    o = r(4913),
                    i = r(283),
                    c = r(9433);
                t.exports = function (t, e, r, a) {
                    a || (a = {});
                    var s = a.enumerable,
                        u = void 0 !== a.name ? a.name : e;
                    if (n(r) && i(r, u, a), a.global) s ? t[e] = r : c(e, r);
                    else {
                        try {
                            a.unsafe ? t[e] && (s = !0) : delete t[e]
                        } catch (l) {}
                        s ? t[e] = r : o.f(t, e, {
                            value: r,
                            enumerable: !1,
                            configurable: !a.nonConfigurable,
                            writable: !a.nonWritable
                        })
                    }
                    return t
                }
            },
            9433: function (t, e, r) {
                "use strict";
                var n = r(4576),
                    o = Object.defineProperty;
                t.exports = function (t, e) {
                    try {
                        o(n, t, {
                            value: e,
                            configurable: !0,
                            writable: !0
                        })
                    } catch (r) {
                        n[t] = e
                    }
                    return e
                }
            },
            3724: function (t, e, r) {
                "use strict";
                var n = r(9039);
                t.exports = !n((function () {
                    return 7 !== Object.defineProperty({}, 1, {
                        get: function () {
                            return 7
                        }
                    })[1]
                }))
            },
            4055: function (t, e, r) {
                "use strict";
                var n = r(4576),
                    o = r(34),
                    i = n.document,
                    c = o(i) && o(i.createElement);
                t.exports = function (t) {
                    return c ? i.createElement(t) : {}
                }
            },
            6837: function (t) {
                "use strict";
                var e = TypeError,
                    r = 9007199254740991;
                t.exports = function (t) {
                    if (t > r) throw e("Maximum allowed index exceeded");
                    return t
                }
            },
            5002: function (t) {
                "use strict";
                t.exports = {
                    IndexSizeError: {
                        s: "INDEX_SIZE_ERR",
                        c: 1,
                        m: 1
                    },
                    DOMStringSizeError: {
                        s: "DOMSTRING_SIZE_ERR",
                        c: 2,
                        m: 0
                    },
                    HierarchyRequestError: {
                        s: "HIERARCHY_REQUEST_ERR",
                        c: 3,
                        m: 1
                    },
                    WrongDocumentError: {
                        s: "WRONG_DOCUMENT_ERR",
                        c: 4,
                        m: 1
                    },
                    InvalidCharacterError: {
                        s: "INVALID_CHARACTER_ERR",
                        c: 5,
                        m: 1
                    },
                    NoDataAllowedError: {
                        s: "NO_DATA_ALLOWED_ERR",
                        c: 6,
                        m: 0
                    },
                    NoModificationAllowedError: {
                        s: "NO_MODIFICATION_ALLOWED_ERR",
                        c: 7,
                        m: 1
                    },
                    NotFoundError: {
                        s: "NOT_FOUND_ERR",
                        c: 8,
                        m: 1
                    },
                    NotSupportedError: {
                        s: "NOT_SUPPORTED_ERR",
                        c: 9,
                        m: 1
                    },
                    InUseAttributeError: {
                        s: "INUSE_ATTRIBUTE_ERR",
                        c: 10,
                        m: 1
                    },
                    InvalidStateError: {
                        s: "INVALID_STATE_ERR",
                        c: 11,
                        m: 1
                    },
                    SyntaxError: {
                        s: "SYNTAX_ERR",
                        c: 12,
                        m: 1
                    },
                    InvalidModificationError: {
                        s: "INVALID_MODIFICATION_ERR",
                        c: 13,
                        m: 1
                    },
                    NamespaceError: {
                        s: "NAMESPACE_ERR",
                        c: 14,
                        m: 1
                    },
                    InvalidAccessError: {
                        s: "INVALID_ACCESS_ERR",
                        c: 15,
                        m: 1
                    },
                    ValidationError: {
                        s: "VALIDATION_ERR",
                        c: 16,
                        m: 0
                    },
                    TypeMismatchError: {
                        s: "TYPE_MISMATCH_ERR",
                        c: 17,
                        m: 1
                    },
                    SecurityError: {
                        s: "SECURITY_ERR",
                        c: 18,
                        m: 1
                    },
                    NetworkError: {
                        s: "NETWORK_ERR",
                        c: 19,
                        m: 1
                    },
                    AbortError: {
                        s: "ABORT_ERR",
                        c: 20,
                        m: 1
                    },
                    URLMismatchError: {
                        s: "URL_MISMATCH_ERR",
                        c: 21,
                        m: 1
                    },
                    QuotaExceededError: {
                        s: "QUOTA_EXCEEDED_ERR",
                        c: 22,
                        m: 1
                    },
                    TimeoutError: {
                        s: "TIMEOUT_ERR",
                        c: 23,
                        m: 1
                    },
                    InvalidNodeTypeError: {
                        s: "INVALID_NODE_TYPE_ERR",
                        c: 24,
                        m: 1
                    },
                    DataCloneError: {
                        s: "DATA_CLONE_ERR",
                        c: 25,
                        m: 1
                    }
                }
            },
            8727: function (t) {
                "use strict";
                t.exports = ["constructor", "hasOwnProperty", "isPrototypeOf", "propertyIsEnumerable", "toLocaleString", "toString", "valueOf"]
            },
            2839: function (t, e, r) {
                "use strict";
                var n = r(4576),
                    o = n.navigator,
                    i = o && o.userAgent;
                t.exports = i ? String(i) : ""
            },
            9519: function (t, e, r) {
                "use strict";
                var n, o, i = r(4576),
                    c = r(2839),
                    a = i.process,
                    s = i.Deno,
                    u = a && a.versions || s && s.version,
                    l = u && u.v8;
                l && (n = l.split("."), o = n[0] > 0 && n[0] < 4 ? 1 : +(n[0] + n[1])), !o && c && (n = c.match(/Edge\/(\d+)/), (!n || n[1] >= 74) && (n = c.match(/Chrome\/(\d+)/), n && (o = +n[1]))), t.exports = o
            },
            6193: function (t, e, r) {
                "use strict";
                var n = r(9504),
                    o = Error,
                    i = n("".replace),
                    c = function (t) {
                        return String(new o(t).stack)
                    }("zxcasd"),
                    a = /\n\s*at [^:]*:[^\n]*/,
                    s = a.test(c);
                t.exports = function (t, e) {
                    if (s && "string" == typeof t && !o.prepareStackTrace)
                        while (e--) t = i(t, a, "");
                    return t
                }
            },
            6518: function (t, e, r) {
                "use strict";
                var n = r(4576),
                    o = r(7347).f,
                    i = r(6699),
                    c = r(6840),
                    a = r(9433),
                    s = r(7740),
                    u = r(2796);
                t.exports = function (t, e) {
                    var r, l, f, p, d, m, g = t.target,
                        v = t.global,
                        y = t.stat;
                    if (l = v ? n : y ? n[g] || a(g, {}) : n[g] && n[g].prototype, l)
                        for (f in e) {
                            if (d = e[f], t.dontCallGetSet ? (m = o(l, f), p = m && m.value) : p = l[f], r = u(v ? f : g + (y ? "." : "#") + f, t.forced), !r && void 0 !== p) {
                                if (typeof d == typeof p) continue;
                                s(d, p)
                            }(t.sham || p && p.sham) && i(d, "sham", !0), c(l, f, d, t)
                        }
                }
            },
            9039: function (t) {
                "use strict";
                t.exports = function (t) {
                    try {
                        return !!t()
                    } catch (e) {
                        return !0
                    }
                }
            },
            616: function (t, e, r) {
                "use strict";
                var n = r(9039);
                t.exports = !n((function () {
                    var t = function () {}.bind();
                    return "function" != typeof t || t.hasOwnProperty("prototype")
                }))
            },
            9565: function (t, e, r) {
                "use strict";
                var n = r(616),
                    o = Function.prototype.call;
                t.exports = n ? o.bind(o) : function () {
                    return o.apply(o, arguments)
                }
            },
            350: function (t, e, r) {
                "use strict";
                var n = r(3724),
                    o = r(9297),
                    i = Function.prototype,
                    c = n && Object.getOwnPropertyDescriptor,
                    a = o(i, "name"),
                    s = a && "something" === function () {}.name,
                    u = a && (!n || n && c(i, "name").configurable);
                t.exports = {
                    EXISTS: a,
                    PROPER: s,
                    CONFIGURABLE: u
                }
            },
            6706: function (t, e, r) {
                "use strict";
                var n = r(9504),
                    o = r(9306);
                t.exports = function (t, e, r) {
                    try {
                        return n(o(Object.getOwnPropertyDescriptor(t, e)[r]))
                    } catch (i) {}
                }
            },
            9504: function (t, e, r) {
                "use strict";
                var n = r(616),
                    o = Function.prototype,
                    i = o.call,
                    c = n && o.bind.bind(i, i);
                t.exports = n ? c : function (t) {
                    return function () {
                        return i.apply(t, arguments)
                    }
                }
            },
            7751: function (t, e, r) {
                "use strict";
                var n = r(4576),
                    o = r(4901),
                    i = function (t) {
                        return o(t) ? t : void 0
                    };
                t.exports = function (t, e) {
                    return arguments.length < 2 ? i(n[t]) : n[t] && n[t][e]
                }
            },
            5966: function (t, e, r) {
                "use strict";
                var n = r(9306),
                    o = r(4117);
                t.exports = function (t, e) {
                    var r = t[e];
                    return o(r) ? void 0 : n(r)
                }
            },
            4576: function (t, e, r) {
                "use strict";
                var n = function (t) {
                    return t && t.Math === Math && t
                };
                t.exports = n("object" == typeof globalThis && globalThis) || n("object" == typeof window && window) || n("object" == typeof self && self) || n("object" == typeof r.g && r.g) || n("object" == typeof this && this) || function () {
                    return this
                }() || Function("return this")()
            },
            9297: function (t, e, r) {
                "use strict";
                var n = r(9504),
                    o = r(8981),
                    i = n({}.hasOwnProperty);
                t.exports = Object.hasOwn || function (t, e) {
                    return i(o(t), e)
                }
            },
            421: function (t) {
                "use strict";
                t.exports = {}
            },
            5917: function (t, e, r) {
                "use strict";
                var n = r(3724),
                    o = r(9039),
                    i = r(4055);
                t.exports = !n && !o((function () {
                    return 7 !== Object.defineProperty(i("div"), "a", {
                        get: function () {
                            return 7
                        }
                    }).a
                }))
            },
            7055: function (t, e, r) {
                "use strict";
                var n = r(9504),
                    o = r(9039),
                    i = r(2195),
                    c = Object,
                    a = n("".split);
                t.exports = o((function () {
                    return !c("z").propertyIsEnumerable(0)
                })) ? function (t) {
                    return "String" === i(t) ? a(t, "") : c(t)
                } : c
            },
            3167: function (t, e, r) {
                "use strict";
                var n = r(4901),
                    o = r(34),
                    i = r(2967);
                t.exports = function (t, e, r) {
                    var c, a;
                    return i && n(c = e.constructor) && c !== r && o(a = c.prototype) && a !== r.prototype && i(t, a), t
                }
            },
            3706: function (t, e, r) {
                "use strict";
                var n = r(9504),
                    o = r(4901),
                    i = r(7629),
                    c = n(Function.toString);
                o(i.inspectSource) || (i.inspectSource = function (t) {
                    return c(t)
                }), t.exports = i.inspectSource
            },
            1181: function (t, e, r) {
                "use strict";
                var n, o, i, c = r(8622),
                    a = r(4576),
                    s = r(34),
                    u = r(6699),
                    l = r(9297),
                    f = r(7629),
                    p = r(6119),
                    d = r(421),
                    m = "Object already initialized",
                    g = a.TypeError,
                    v = a.WeakMap,
                    y = function (t) {
                        return i(t) ? o(t) : n(t, {})
                    },
                    h = function (t) {
                        return function (e) {
                            var r;
                            if (!s(e) || (r = o(e)).type !== t) throw new g("Incompatible receiver, " + t + " required");
                            return r
                        }
                    };
                if (c || f.state) {
                    var b = f.state || (f.state = new v);
                    b.get = b.get, b.has = b.has, b.set = b.set, n = function (t, e) {
                        if (b.has(t)) throw new g(m);
                        return e.facade = t, b.set(t, e), e
                    }, o = function (t) {
                        return b.get(t) || {}
                    }, i = function (t) {
                        return b.has(t)
                    }
                } else {
                    var w = p("state");
                    d[w] = !0, n = function (t, e) {
                        if (l(t, w)) throw new g(m);
                        return e.facade = t, u(t, w, e), e
                    }, o = function (t) {
                        return l(t, w) ? t[w] : {}
                    }, i = function (t) {
                        return l(t, w)
                    }
                }
                t.exports = {
                    set: n,
                    get: o,
                    has: i,
                    enforce: y,
                    getterFor: h
                }
            },
            4376: function (t, e, r) {
                "use strict";
                var n = r(2195);
                t.exports = Array.isArray || function (t) {
                    return "Array" === n(t)
                }
            },
            4901: function (t) {
                "use strict";
                var e = "object" == typeof document && document.all;
                t.exports = "undefined" == typeof e && void 0 !== e ? function (t) {
                    return "function" == typeof t || t === e
                } : function (t) {
                    return "function" == typeof t
                }
            },
            2796: function (t, e, r) {
                "use strict";
                var n = r(9039),
                    o = r(4901),
                    i = /#|\.prototype\./,
                    c = function (t, e) {
                        var r = s[a(t)];
                        return r === l || r !== u && (o(e) ? n(e) : !!e)
                    },
                    a = c.normalize = function (t) {
                        return String(t).replace(i, ".").toLowerCase()
                    },
                    s = c.data = {},
                    u = c.NATIVE = "N",
                    l = c.POLYFILL = "P";
                t.exports = c
            },
            4117: function (t) {
                "use strict";
                t.exports = function (t) {
                    return null === t || void 0 === t
                }
            },
            34: function (t, e, r) {
                "use strict";
                var n = r(4901);
                t.exports = function (t) {
                    return "object" == typeof t ? null !== t : n(t)
                }
            },
            3925: function (t, e, r) {
                "use strict";
                var n = r(34);
                t.exports = function (t) {
                    return n(t) || null === t
                }
            },
            6395: function (t) {
                "use strict";
                t.exports = !1
            },
            757: function (t, e, r) {
                "use strict";
                var n = r(7751),
                    o = r(4901),
                    i = r(1625),
                    c = r(7040),
                    a = Object;
                t.exports = c ? function (t) {
                    return "symbol" == typeof t
                } : function (t) {
                    var e = n("Symbol");
                    return o(e) && i(e.prototype, a(t))
                }
            },
            6198: function (t, e, r) {
                "use strict";
                var n = r(8014);
                t.exports = function (t) {
                    return n(t.length)
                }
            },
            283: function (t, e, r) {
                "use strict";
                var n = r(9504),
                    o = r(9039),
                    i = r(4901),
                    c = r(9297),
                    a = r(3724),
                    s = r(350).CONFIGURABLE,
                    u = r(3706),
                    l = r(1181),
                    f = l.enforce,
                    p = l.get,
                    d = String,
                    m = Object.defineProperty,
                    g = n("".slice),
                    v = n("".replace),
                    y = n([].join),
                    h = a && !o((function () {
                        return 8 !== m((function () {}), "length", {
                            value: 8
                        }).length
                    })),
                    b = String(String).split("String"),
                    w = t.exports = function (t, e, r) {
                        "Symbol(" === g(d(e), 0, 7) && (e = "[" + v(d(e), /^Symbol\(([^)]*)\).*$/, "$1") + "]"), r && r.getter && (e = "get " + e), r && r.setter && (e = "set " + e), (!c(t, "name") || s && t.name !== e) && (a ? m(t, "name", {
                            value: e,
                            configurable: !0
                        }) : t.name = e), h && r && c(r, "arity") && t.length !== r.arity && m(t, "length", {
                            value: r.arity
                        });
                        try {
                            r && c(r, "constructor") && r.constructor ? a && m(t, "prototype", {
                                writable: !1
                            }) : t.prototype && (t.prototype = void 0)
                        } catch (o) {}
                        var n = f(t);
                        return c(n, "source") || (n.source = y(b, "string" == typeof e ? e : "")), t
                    };
                Function.prototype.toString = w((function () {
                    return i(this) && p(this).source || u(this)
                }), "toString")
            },
            741: function (t) {
                "use strict";
                var e = Math.ceil,
                    r = Math.floor;
                t.exports = Math.trunc || function (t) {
                    var n = +t;
                    return (n > 0 ? r : e)(n)
                }
            },
            2603: function (t, e, r) {
                "use strict";
                var n = r(655);
                t.exports = function (t, e) {
                    return void 0 === t ? arguments.length < 2 ? "" : e : n(t)
                }
            },
            4913: function (t, e, r) {
                "use strict";
                var n = r(3724),
                    o = r(5917),
                    i = r(8686),
                    c = r(8551),
                    a = r(6969),
                    s = TypeError,
                    u = Object.defineProperty,
                    l = Object.getOwnPropertyDescriptor,
                    f = "enumerable",
                    p = "configurable",
                    d = "writable";
                e.f = n ? i ? function (t, e, r) {
                    if (c(t), e = a(e), c(r), "function" === typeof t && "prototype" === e && "value" in r && d in r && !r[d]) {
                        var n = l(t, e);
                        n && n[d] && (t[e] = r.value, r = {
                            configurable: p in r ? r[p] : n[p],
                            enumerable: f in r ? r[f] : n[f],
                            writable: !1
                        })
                    }
                    return u(t, e, r)
                } : u : function (t, e, r) {
                    if (c(t), e = a(e), c(r), o) try {
                        return u(t, e, r)
                    } catch (n) {}
                    if ("get" in r || "set" in r) throw new s("Accessors not supported");
                    return "value" in r && (t[e] = r.value), t
                }
            },
            7347: function (t, e, r) {
                "use strict";
                var n = r(3724),
                    o = r(9565),
                    i = r(8773),
                    c = r(6980),
                    a = r(5397),
                    s = r(6969),
                    u = r(9297),
                    l = r(5917),
                    f = Object.getOwnPropertyDescriptor;
                e.f = n ? f : function (t, e) {
                    if (t = a(t), e = s(e), l) try {
                        return f(t, e)
                    } catch (r) {}
                    if (u(t, e)) return c(!o(i.f, t, e), t[e])
                }
            },
            8480: function (t, e, r) {
                "use strict";
                var n = r(1828),
                    o = r(8727),
                    i = o.concat("length", "prototype");
                e.f = Object.getOwnPropertyNames || function (t) {
                    return n(t, i)
                }
            },
            3717: function (t, e) {
                "use strict";
                e.f = Object.getOwnPropertySymbols
            },
            1625: function (t, e, r) {
                "use strict";
                var n = r(9504);
                t.exports = n({}.isPrototypeOf)
            },
            1828: function (t, e, r) {
                "use strict";
                var n = r(9504),
                    o = r(9297),
                    i = r(5397),
                    c = r(9617).indexOf,
                    a = r(421),
                    s = n([].push);
                t.exports = function (t, e) {
                    var r, n = i(t),
                        u = 0,
                        l = [];
                    for (r in n) !o(a, r) && o(n, r) && s(l, r);
                    while (e.length > u) o(n, r = e[u++]) && (~c(l, r) || s(l, r));
                    return l
                }
            },
            8773: function (t, e) {
                "use strict";
                var r = {}.propertyIsEnumerable,
                    n = Object.getOwnPropertyDescriptor,
                    o = n && !r.call({
                        1: 2
                    }, 1);
                e.f = o ? function (t) {
                    var e = n(this, t);
                    return !!e && e.enumerable
                } : r
            },
            2967: function (t, e, r) {
                "use strict";
                var n = r(6706),
                    o = r(34),
                    i = r(7750),
                    c = r(3506);
                t.exports = Object.setPrototypeOf || ("__proto__" in {} ? function () {
                    var t, e = !1,
                        r = {};
                    try {
                        t = n(Object.prototype, "__proto__", "set"), t(r, []), e = r instanceof Array
                    } catch (a) {}
                    return function (r, n) {
                        return i(r), c(n), o(r) ? (e ? t(r, n) : r.__proto__ = n, r) : r
                    }
                }() : void 0)
            },
            4270: function (t, e, r) {
                "use strict";
                var n = r(9565),
                    o = r(4901),
                    i = r(34),
                    c = TypeError;
                t.exports = function (t, e) {
                    var r, a;
                    if ("string" === e && o(r = t.toString) && !i(a = n(r, t))) return a;
                    if (o(r = t.valueOf) && !i(a = n(r, t))) return a;
                    if ("string" !== e && o(r = t.toString) && !i(a = n(r, t))) return a;
                    throw new c("Can't convert object to primitive value")
                }
            },
            5031: function (t, e, r) {
                "use strict";
                var n = r(7751),
                    o = r(9504),
                    i = r(8480),
                    c = r(3717),
                    a = r(8551),
                    s = o([].concat);
                t.exports = n("Reflect", "ownKeys") || function (t) {
                    var e = i.f(a(t)),
                        r = c.f;
                    return r ? s(e, r(t)) : e
                }
            },
            7750: function (t, e, r) {
                "use strict";
                var n = r(4117),
                    o = TypeError;
                t.exports = function (t) {
                    if (n(t)) throw new o("Can't call method on " + t);
                    return t
                }
            },
            6119: function (t, e, r) {
                "use strict";
                var n = r(5745),
                    o = r(3392),
                    i = n("keys");
                t.exports = function (t) {
                    return i[t] || (i[t] = o(t))
                }
            },
            7629: function (t, e, r) {
                "use strict";
                var n = r(6395),
                    o = r(4576),
                    i = r(9433),
                    c = "__core-js_shared__",
                    a = t.exports = o[c] || i(c, {});
                (a.versions || (a.versions = [])).push({
                    version: "3.38.1",
                    mode: n ? "pure" : "global",
                    copyright: "© 2014-2024 Denis Pushkarev (zloirock.ru)",
                    license: "https://github.com/zloirock/core-js/blob/v3.38.1/LICENSE",
                    source: "https://github.com/zloirock/core-js"
                })
            },
            5745: function (t, e, r) {
                "use strict";
                var n = r(7629);
                t.exports = function (t, e) {
                    return n[t] || (n[t] = e || {})
                }
            },
            4495: function (t, e, r) {
                "use strict";
                var n = r(9519),
                    o = r(9039),
                    i = r(4576),
                    c = i.String;
                t.exports = !!Object.getOwnPropertySymbols && !o((function () {
                    var t = Symbol("symbol detection");
                    return !c(t) || !(Object(t) instanceof Symbol) || !Symbol.sham && n && n < 41
                }))
            },
            5610: function (t, e, r) {
                "use strict";
                var n = r(1291),
                    o = Math.max,
                    i = Math.min;
                t.exports = function (t, e) {
                    var r = n(t);
                    return r < 0 ? o(r + e, 0) : i(r, e)
                }
            },
            5397: function (t, e, r) {
                "use strict";
                var n = r(7055),
                    o = r(7750);
                t.exports = function (t) {
                    return n(o(t))
                }
            },
            1291: function (t, e, r) {
                "use strict";
                var n = r(741);
                t.exports = function (t) {
                    var e = +t;
                    return e !== e || 0 === e ? 0 : n(e)
                }
            },
            8014: function (t, e, r) {
                "use strict";
                var n = r(1291),
                    o = Math.min;
                t.exports = function (t) {
                    var e = n(t);
                    return e > 0 ? o(e, 9007199254740991) : 0
                }
            },
            8981: function (t, e, r) {
                "use strict";
                var n = r(7750),
                    o = Object;
                t.exports = function (t) {
                    return o(n(t))
                }
            },
            2777: function (t, e, r) {
                "use strict";
                var n = r(9565),
                    o = r(34),
                    i = r(757),
                    c = r(5966),
                    a = r(4270),
                    s = r(8227),
                    u = TypeError,
                    l = s("toPrimitive");
                t.exports = function (t, e) {
                    if (!o(t) || i(t)) return t;
                    var r, s = c(t, l);
                    if (s) {
                        if (void 0 === e && (e = "default"), r = n(s, t, e), !o(r) || i(r)) return r;
                        throw new u("Can't convert object to primitive value")
                    }
                    return void 0 === e && (e = "number"), a(t, e)
                }
            },
            6969: function (t, e, r) {
                "use strict";
                var n = r(2777),
                    o = r(757);
                t.exports = function (t) {
                    var e = n(t, "string");
                    return o(e) ? e : e + ""
                }
            },
            2140: function (t, e, r) {
                "use strict";
                var n = r(8227),
                    o = n("toStringTag"),
                    i = {};
                i[o] = "z", t.exports = "[object z]" === String(i)
            },
            655: function (t, e, r) {
                "use strict";
                var n = r(6955),
                    o = String;
                t.exports = function (t) {
                    if ("Symbol" === n(t)) throw new TypeError("Cannot convert a Symbol value to a string");
                    return o(t)
                }
            },
            6823: function (t) {
                "use strict";
                var e = String;
                t.exports = function (t) {
                    try {
                        return e(t)
                    } catch (r) {
                        return "Object"
                    }
                }
            },
            3392: function (t, e, r) {
                "use strict";
                var n = r(9504),
                    o = 0,
                    i = Math.random(),
                    c = n(1..toString);
                t.exports = function (t) {
                    return "Symbol(" + (void 0 === t ? "" : t) + ")_" + c(++o + i, 36)
                }
            },
            7040: function (t, e, r) {
                "use strict";
                var n = r(4495);
                t.exports = n && !Symbol.sham && "symbol" == typeof Symbol.iterator
            },
            8686: function (t, e, r) {
                "use strict";
                var n = r(3724),
                    o = r(9039);
                t.exports = n && o((function () {
                    return 42 !== Object.defineProperty((function () {}), "prototype", {
                        value: 42,
                        writable: !1
                    }).prototype
                }))
            },
            8622: function (t, e, r) {
                "use strict";
                var n = r(4576),
                    o = r(4901),
                    i = n.WeakMap;
                t.exports = o(i) && /native code/.test(String(i))
            },
            8227: function (t, e, r) {
                "use strict";
                var n = r(4576),
                    o = r(5745),
                    i = r(9297),
                    c = r(3392),
                    a = r(4495),
                    s = r(7040),
                    u = n.Symbol,
                    l = o("wks"),
                    f = s ? u["for"] || u : u && u.withoutSetter || c;
                t.exports = function (t) {
                    return i(l, t) || (l[t] = a && i(u, t) ? u[t] : f("Symbol." + t)), l[t]
                }
            },
            4114: function (t, e, r) {
                "use strict";
                var n = r(6518),
                    o = r(8981),
                    i = r(6198),
                    c = r(4527),
                    a = r(6837),
                    s = r(9039),
                    u = s((function () {
                        return 4294967297 !== [].push.call({
                            length: 4294967296
                        }, 1)
                    })),
                    l = function () {
                        try {
                            Object.defineProperty([], "length", {
                                writable: !1
                            }).push()
                        } catch (t) {
                            return t instanceof TypeError
                        }
                    },
                    f = u || !l();
                n({
                    target: "Array",
                    proto: !0,
                    arity: 1,
                    forced: f
                }, {
                    push: function (t) {
                        var e = o(this),
                            r = i(e),
                            n = arguments.length;
                        a(r + n);
                        for (var s = 0; s < n; s++) e[r] = arguments[s], r++;
                        return c(e, r), r
                    }
                })
            },
            4979: function (t, e, r) {
                "use strict";
                var n = r(6518),
                    o = r(4576),
                    i = r(7751),
                    c = r(6980),
                    a = r(4913).f,
                    s = r(9297),
                    u = r(679),
                    l = r(3167),
                    f = r(2603),
                    p = r(5002),
                    d = r(6193),
                    m = r(3724),
                    g = r(6395),
                    v = "DOMException",
                    y = i("Error"),
                    h = i(v),
                    b = function () {
                        u(this, w);
                        var t = arguments.length,
                            e = f(t < 1 ? void 0 : arguments[0]),
                            r = f(t < 2 ? void 0 : arguments[1], "Error"),
                            n = new h(e, r),
                            o = new y(e);
                        return o.name = v, a(n, "stack", c(1, d(o.stack, 1))), l(n, this, b), n
                    },
                    w = b.prototype = h.prototype,
                    E = "stack" in new y(v),
                    S = "stack" in new h(1, 2),
                    x = h && m && Object.getOwnPropertyDescriptor(o, v),
                    I = !!x && !(x.writable && x.configurable),
                    O = E && !I && !S;
                n({
                    global: !0,
                    constructor: !0,
                    forced: g || O
                }, {
                    DOMException: O ? b : h
                });
                var R = i(v),
                    T = R.prototype;
                if (T.constructor !== R)
                    for (var _ in g || a(T, "constructor", c(1, R)), p)
                        if (s(p, _)) {
                            var A = p[_],
                                P = A.s;
                            s(R, P) || a(R, P, c(6, A.c))
                        }
            }
        },
        e = {};

    function r(n) {
        var o = e[n];
        if (void 0 !== o) return o.exports;
        var i = e[n] = {
            exports: {}
        };
        return t[n].call(i.exports, i, i.exports, r), i.exports
    }! function () {
        r.g = function () {
            if ("object" === typeof globalThis) return globalThis;
            try {
                return this || new Function("return this")()
            } catch (t) {
                if ("object" === typeof window) return window
            }
        }()
    }();
    r(4114), r(4979);
    let n = !1;
    async function o(t) {
        t({
            username: null,
            t: null,
            urlError: false,
            currentUrl: window.location.hostname.replace("www.", "")
        })
    }

    function i() {
        const t = document.querySelectorAll('div[class*="deep-history"]');
        if (t.length > 0) {
            var e = t[0].getElementsByTagName("input");
            for (let t = 0; t < e.length; t++) {
                const r = e[t];
                if ("checkbox" === r.type) return r.checked
            }
        }
        return !1
    }

    function c() {
        let t = document.querySelector('button[aria-label="Open Strategy Tester"]');
        t && t.click()
    }

    function a() {
        const t = document.querySelectorAll('div[class*="deep-history"]');
        if (t.length > 0) {
            var e = t[0].getElementsByTagName("button");
            for (let t = 0; t < e.length; t++) {
                const r = e[t];
                if ("Generate report" === r.innerText) {
                    const t = r.disabled;
                    return r.click(), t
                }
            }
        }
    }
    async function s(t, e) {
        let r = document.querySelectorAll('input[class^="search"]');
        while (r.length > 0) await v(100), r = document.querySelectorAll('input[class^="search"]');
        let n = document.createElement("script");
        n.src = chrome.runtime.getURL("js/changeTimeframeScript.js"), (document.head || document.documentElement).appendChild(n), n.onload = () => n.remove(), await v(200);
        const o = document.querySelectorAll('div[class^="dialogInner"]'),
            i = o[0].getElementsByTagName("input");
        i.length > 0 && (t = t.replace("m", ""), i[0].value = t, i[0].dispatchEvent(new Event("change", {
            bubbles: !0
        })), i[0].dispatchEvent(new Event("blur", {
            bubbles: !0
        })), n = document.createElement("script"), n.src = chrome.runtime.getURL("js/submitFormScript.js"), (document.head || document.documentElement).appendChild(n), n.onload = () => n.remove()), localStorage.setItem("isLoading", !0), await v(200), document.dispatchEvent(new CustomEvent("readTrades", {}));
        while ("true" === localStorage.getItem("isLoading")) await v(50);
        let c = [];
        const a = document.querySelectorAll("[data-name='indicator-properties-dialog']");
        if (a.length > 0) {
            const t = document.querySelectorAll("[inputmode='numeric']");
            for (let e = 0; e < t.length; e++) {
                const r = t[e];
                c.push({
                    fieldNo: e + 1,
                    currentValue: r.value
                })
            }
        }
        e(c)
    }
    async function u(t) {
        const e = document.getElementById("header-toolbar-symbol-search");
        if (c(), i() || p(), "undefined" !== typeof e) {
            e.click(), await v(200);
            const r = document.querySelectorAll('input[class^="search"]');
            if (r.length > 0) {
                r[0].value = t;
                const e = document.createElement("script");
                e.src = chrome.runtime.getURL("js/dispatchEnterKey.js"), (document.head || document.documentElement).appendChild(e), e.onload = () => e.remove()
            }
        }
    }
    async function l(t, e) {
        const r = document.querySelectorAll("[data-name='indicator-properties-dialog']");
        if (r.length > 0) {
            const r = i();
            let l = document.querySelector('[class^="reportContainer"]');
            (null == l || r) && (l = document.querySelector('[class^="backtesting"]'));
            let p = !0;
            const d = {
                    attributes: !0,
                    childList: !0,
                    subtree: !0
                },
                y = function (t, e) {
                    p = !1, r && !n && f()
                },
                h = new MutationObserver(y);
            h.observe(l, d);
            const b = document.querySelectorAll("[inputmode='numeric']");
            let w = !1;
            if (b.length > 0) {
                for (let e = 0; e < t.fields.length; e++) {
                    b[parseInt(t.fields[e].fieldNo) - 1].value != t.fields[e].currentValue && (w = !0), b[parseInt(t.fields[e].fieldNo) - 1].value = t.fields[e].currentValue, b[parseInt(t.fields[e].fieldNo) - 1].dispatchEvent(new Event("change", {
                        bubbles: !0
                    })), b[parseInt(t.fields[e].fieldNo) - 1].dispatchEvent(new Event("blur", {
                        bubbles: !0
                    }));
                    var o = 13,
                        c = "keydown",
                        s = new KeyboardEvent(c, {
                            altKey: !1,
                            bubbles: !0,
                            cancelBubble: !1,
                            cancelable: !0,
                            charCode: "Enter",
                            code: "Enter",
                            composed: !0,
                            ctrlKey: !1,
                            currentTarget: null,
                            defaultPrevented: !0,
                            detail: 0,
                            eventPhase: 0,
                            isComposing: !1,
                            isTrusted: !1,
                            key: "Enter",
                            keyCode: o,
                            location: 0,
                            metaKey: !1,
                            repeat: !1,
                            returnValue: !1,
                            shiftKey: !1,
                            sourceCapabilities: new InputDeviceCapabilities({
                                firesTouchEvents: !1
                            }),
                            timeStamp: performance.now(),
                            type: c,
                            view: window,
                            which: o
                        });
                    b[parseInt(t.fields[e].fieldNo) - 1].dispatchEvent(s)
                }
                if (r) {
                    await v(200);
                    const r = a();
                    r && (p = !1);
                    while (p && w) await v(50);
                    await g(t, e)
                } else {
                    localStorage.setItem("isLoading", !0);
                    while (p && w) await v(50);
                    if (h.disconnect(), await m(), document.dispatchEvent(new CustomEvent("readTrades", {})), null != t.waitTimespan) {
                        let e = parseInt(t.waitTimespan);
                        isNaN(e) || await v(1e3 * e)
                    }
                    while ("true" === localStorage.getItem("isLoading")) await v(50);
                    let r = !1,
                        n = "";
                    "true" === localStorage.getItem("error") && (r = !0, n = 'No backtest data available. Make sure you have selected your strategy in the "Strategy Tester" tab.');
                    const o = localStorage.getItem("netProfitAll"),
                        i = localStorage.getItem("percentProfitAll"),
                        c = localStorage.getItem("netProfitLong"),
                        a = localStorage.getItem("percentProfitLong"),
                        s = localStorage.getItem("netProfitShort"),
                        l = localStorage.getItem("percentProfitShort"),
                        f = localStorage.getItem("profitFactor"),
                        d = localStorage.getItem("drawDown"),

                        y = localStorage.getItem("avgBars"),
                        b = localStorage.getItem("totalTrades"),
                        E = localStorage.getItem("sharpRatio"),
                        S = localStorage.getItem("sortinoRatio");
                    var u = localStorage.getItem("is_suspicious");
                    null != u && localStorage.removeItem("is_suspicious"), e({
                        result: {
                            netProfitAll: o,
                            percentProfitAll: i,
                            netProfitLong: c,
                            percentProfitLong: a,
                            netProfitShort: s,
                            percentProfitShort: l,
                            profitFactor: f,
                            drawDown: d,
                            avgBars: y,
                            totalTrades: b,
                            sharpRatio: E,
                            sortinoRatio: S,

                        },
                        error: r,
                        errorMsg: n
                    })
                }
            }
        }
        e({
            error: !0,
            errorMsg: "No strategy property window found. Open the input window of your strategy by clicking on the settings button next to your strategy name."
        })
    }
    async function f() {
        let t = null,
            e = 0;
        while (null === t && e < 10) t = document.getElementById("Performance"), null === t && (await v(100), e++);
        null !== t && (t.click(), n = !0)
    }
    async function p() {
        let t = null,
            e = 0;
        while (null === t && e < 10) t = document.getElementById("Overview"), null === t && (await v(100), e++);
        null !== t && t.click()
    }

    function d(t) {
        let e = t.querySelector('[class*="percentValue"]');
        if (e) {
            let t = e.innerText;
            if (null != t) {
                t = t.replace("%", ""), t = t.replace("−", "-"), t = t.replace(",", "");
                const e = parseFloat(t);
                return isNaN(e) || 0 == e ? e : e / 100
            }
        }
        return null
    }
    async function m() {
        let t = document.querySelector(".deep-history");
        if (t) {
            var e = t.querySelector('[class^="reportContainer"]'),
                r = t.querySelector('[class^="ka-table-wrapper"]');
            while (null == e && null == r) {
                await v(100), t = document.querySelector(".deep-history"), e = t.querySelector('[class^="reportContainer"]'), r = t.querySelector('[class^="ka-table-wrapper"]');
                const n = t.querySelector('[class*="emptyStateIcon"]');
                if (n) break
            }
        }
    }
    async function g(t, e) {
        let r = !1,
            n = "",
            o = !1,
            i = 0,
            c = document.querySelector(".deep-history");
        if (c) {
            var a = c.querySelector('[class^="reportContainer"]');
            while (null == a) {
                await v(100), c = document.querySelector(".deep-history"), a = c.querySelector('[class^="ka-table-wrapper"]');
                const t = c.querySelector('[class*="emptyStateIcon"]');
                if (t && (await v(200), i++, i > 10)) {
                    o = !0;
                    break
                }
            }
            const s = a ? .querySelector(".ka-tbody");
            if (s) {
                let o, i, c, a, u, l, f, p, m, g, y, h;
                const b = s.querySelectorAll(".ka-tr");
                if (b.length > 0) {
                    for (let t = 0; t < b.length; t++) {
                        const e = b[t].querySelectorAll(".ka-cell");
                        let s;
                        const v = e[0].querySelector('[class^="title"]');
                        v && (s = v.innerText);
                        try {
                            switch (s.toLowerCase()) {
                                case "net profit":
                                    o = d(e[1]), c = d(e[2]), u = d(e[3]);
                                    break;
                                case "percent profitable":
                                    i = d(e[1]), a = d(e[2]), l = d(e[3]);
                                    break;
                                case "max drawdown":
                                    p = d(e[1]);
                                    break;
                                case "avg # bars in trades":
                                    m = e[1].firstElementChild.innerText, m && (m = m.replace(",", ""), m = parseInt(m));
                                    break;
                                case "total closed trades":
                                    g = e[1].firstElementChild.innerText, g && (g = g.replace(",", ""), g = parseInt(g));
                                    break;
                                case "profit factor":
                                    f = e[1].firstElementChild.innerText, f && (f = f.replace(",", ""), f = parseFloat(f));
                                    break;
                                case "sharpe ratio":
                                    y = e[1].firstElementChild.innerText, y = y.replace("−", "-"), y = y.replace(",", ""), y && (y = parseFloat(y));
                                    break;
                                case "sortino ratio":
                                    h = e[1].firstElementChild.innerText, h = h.replace("−", "-"), h = h.replace(",", ""), h && (h = parseFloat(h));
                                    break
                            }
                        } catch {
                            r = !0, n = `Error while reading ${s} value.`
                        }
                    }

                    if (null != t.waitTimespan) {
                        let e = parseInt(t.waitTimespan);
                        isNaN(e) || await v(1e3 * e)
                    }
                    e({
                        result: {
                            netProfitAll: o,
                            percentProfitAll: i,
                            netProfitLong: c,
                            percentProfitLong: a,
                            netProfitShort: u,
                            percentProfitShort: l,
                            profitFactor: f,
                            drawDown: p,
                            avgBars: m,
                            totalTrades: g,
                            sharpRatio: y,
                            sortinoRatio: h,

                        },
                        error: r,
                        errorMsg: n
                    })
                } else e({
                    error: !0,
                    errorMsg: "Deep backtest performance summary table not found."
                })
            }
            o && e({
                result: {
                    netProfitAll: 0,
                    percentProfitAll: 0,
                    netProfitLong: 0,
                    percentProfitLong: 0,
                    netProfitShort: 0,
                    percentProfitShort: 0,
                    profitFactor: 0,
                    drawDown: 0,
                    avgBars: 0,
                    totalTrades: 0,
                    sharpRatio: 0,
                    sortinoRatio: 0,

                },
                error: r,
                errorMsg: n
            })
        }
        e({
            error: !0,
            errorMsg: "Deep backtesting container not found. Check if the performance summary tab is activated."
        })
    }

    function v(t) {
        return new Promise((e => setTimeout(e, t)))
    }

    function y(t) {
        let e = document.createElement("script");
        return e.src = chrome.runtime.getURL(t), e.type = "text/javascript", e
    }
    chrome.runtime.onMessage.addListener((function (t, e, r) {
        switch (t.type) {
            case "change_pair":
                u(t.pair), r("ok");
                break;
            case "change_tf":
                s(t.tf, r);
                break;
            case "change_value":
                l(t, r);
                break;
            case "username":
                o(r);
                break
        }
        return !0
    }));
    const h = document.body || document.head || document.documentElement,
        b = chrome.runtime.getManifest(),
        w = b.web_accessible_resources;
    for (let E = 0; E < w.length; E++) {
        const t = w[E],
            e = t.resources;
        for (let r = 0; r < e.length; r++) {
            let t = e[r];
            if (t.endsWith("functions.js")) {
                let e = y(t);
                h.querySelector("script[src*='" + t + "']") || h.appendChild(e)
            }
        }
    }
})();