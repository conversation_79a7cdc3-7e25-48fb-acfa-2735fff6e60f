(function () {
  var e = {
      953: function (e, t, n) {
        "use strict";
        n.d(t, {
          C4: function () {
            return _
          },
          EW: function () {
            return je
          },
          Gc: function () {
            return ve
          },
          IG: function () {
            return Ee
          },
          IJ: function () {
            return Fe
          },
          KR: function () {
            return Re
          },
          Kh: function () {
            return ge
          },
          Pr: function () {
            return Me
          },
          R1: function () {
            return Ne
          },
          X2: function () {
            return c
          },
          bl: function () {
            return E
          },
          fE: function () {
            return ke
          },
          g8: function () {
            return Se
          },
          hV: function () {
            return We
          },
          hZ: function () {
            return B
          },
          i9: function () {
            return Oe
          },
          ju: function () {
            return xe
          },
          lJ: function () {
            return Te
          },
          qA: function () {
            return D
          },
          u4: function () {
            return M
          },
          ux: function () {
            return _e
          },
          wB: function () {
            return $e
          },
          yC: function () {
            return i
          }
        });
        var r = n(33);
        /**
         * @vue/reactivity v3.5.12
         * (c) 2018-present Yuxi (Evan) You and Vue contributors
         * @license MIT
         **/
        let s, o;
        class i {
          constructor(e = !1) {
            this.detached = e, this._active = !0, this.effects = [], this.cleanups = [], this._isPaused = !1, this.parent = s, !e && s && (this.index = (s.scopes || (s.scopes = [])).push(this) - 1)
          }
          get active() {
            return this._active
          }
          pause() {
            if (this._active) {
              let e, t;
              if (this._isPaused = !0, this.scopes)
                for (e = 0, t = this.scopes.length; e < t; e++) this.scopes[e].pause();
              for (e = 0, t = this.effects.length; e < t; e++) this.effects[e].pause()
            }
          }
          resume() {
            if (this._active && this._isPaused) {
              let e, t;
              if (this._isPaused = !1, this.scopes)
                for (e = 0, t = this.scopes.length; e < t; e++) this.scopes[e].resume();
              for (e = 0, t = this.effects.length; e < t; e++) this.effects[e].resume()
            }
          }
          run(e) {
            if (this._active) {
              const t = s;
              try {
                return s = this, e()
              } finally {
                s = t
              }
            } else 0
          }
          on() {
            s = this
          }
          off() {
            s = this.parent
          }
          stop(e) {
            if (this._active) {
              let t, n;
              for (t = 0, n = this.effects.length; t < n; t++) this.effects[t].stop();
              for (t = 0, n = this.cleanups.length; t < n; t++) this.cleanups[t]();
              if (this.scopes)
                for (t = 0, n = this.scopes.length; t < n; t++) this.scopes[t].stop(!0);
              if (!this.detached && this.parent && !e) {
                const e = this.parent.scopes.pop();
                e && e !== this && (this.parent.scopes[this.index] = e, e.index = this.index)
              }
              this.parent = void 0, this._active = !1
            }
          }
        }

        function a() {
          return s
        }
        const l = new WeakSet;
        class c {
          constructor(e) {
            this.fn = e, this.deps = void 0, this.depsTail = void 0, this.flags = 5, this.next = void 0, this.cleanup = void 0, this.scheduler = void 0, s && s.active && s.effects.push(this)
          }
          pause() {
            this.flags |= 64
          }
          resume() {
            64 & this.flags && (this.flags &= -65, l.has(this) && (l.delete(this), this.trigger()))
          }
          notify() {
            2 & this.flags && !(32 & this.flags) || 8 & this.flags || h(this)
          }
          run() {
            if (!(1 & this.flags)) return this.fn();
            this.flags |= 2, T(this), g(this);
            const e = o,
              t = k;
            o = this, k = !0;
            try {
              return this.fn()
            } finally {
              0,
              v(this),
              o = e,
              k = t,
              this.flags &= -3
            }
          }
          stop() {
            if (1 & this.flags) {
              for (let e = this.deps; e; e = e.nextDep) S(e);
              this.deps = this.depsTail = void 0, T(this), this.onStop && this.onStop(), this.flags &= -2
            }
          }
          trigger() {
            64 & this.flags ? l.add(this) : this.scheduler ? this.scheduler() : this.runIfDirty()
          }
          runIfDirty() {
            y(this) && this.run()
          }
          get dirty() {
            return y(this)
          }
        }
        let u, f, d = 0;

        function h(e, t = !1) {
          if (e.flags |= 8, t) return e.next = f, void(f = e);
          e.next = u, u = e
        }

        function p() {
          d++
        }

        function m() {
          if (--d > 0) return;
          if (f) {
            let e = f;
            f = void 0;
            while (e) {
              const t = e.next;
              e.next = void 0, e.flags &= -9, e = t
            }
          }
          let e;
          while (u) {
            let n = u;
            u = void 0;
            while (n) {
              const r = n.next;
              if (n.next = void 0, n.flags &= -9, 1 & n.flags) try {
                n.trigger()
              } catch (t) {
                e || (e = t)
              }
              n = r
            }
          }
          if (e) throw e
        }

        function g(e) {
          for (let t = e.deps; t; t = t.nextDep) t.version = -1, t.prevActiveLink = t.dep.activeLink, t.dep.activeLink = t
        }

        function v(e) {
          let t, n = e.depsTail,
            r = n;
          while (r) {
            const e = r.prevDep; - 1 === r.version ? (r === n && (n = e), S(r), w(r)) : t = r, r.dep.activeLink = r.prevActiveLink, r.prevActiveLink = void 0, r = e
          }
          e.deps = t, e.depsTail = n
        }

        function y(e) {
          for (let t = e.deps; t; t = t.nextDep)
            if (t.dep.version !== t.version || t.dep.computed && (b(t.dep.computed) || t.dep.version !== t.version)) return !0;
          return !!e._dirty
        }

        function b(e) {
          if (4 & e.flags && !(16 & e.flags)) return;
          if (e.flags &= -17, e.globalVersion === C) return;
          e.globalVersion = C;
          const t = e.dep;
          if (e.flags |= 2, t.version > 0 && !e.isSSR && e.deps && !y(e)) return void(e.flags &= -3);
          const n = o,
            s = k;
          o = e, k = !0;
          try {
            g(e);
            const n = e.fn(e._value);
            (0 === t.version || (0, r.$H)(n, e._value)) && (e._value = n, t.version++)
          } catch (i) {
            throw t.version++, i
          } finally {
            o = n, k = s, v(e), e.flags &= -3
          }
        }

        function S(e, t = !1) {
          const {
            dep: n,
            prevSub: r,
            nextSub: s
          } = e;
          if (r && (r.nextSub = s, e.prevSub = void 0), s && (s.prevSub = r, e.nextSub = void 0), n.subs === e && (n.subs = r, !r && n.computed)) {
            n.computed.flags &= -5;
            for (let e = n.computed.deps; e; e = e.nextDep) S(e, !0)
          }
          t || --n.sc || !n.map || n.map.delete(n.key)
        }

        function w(e) {
          const {
            prevDep: t,
            nextDep: n
          } = e;
          t && (t.nextDep = n, e.prevDep = void 0), n && (n.prevDep = t, e.nextDep = void 0)
        }
        let k = !0;
        const x = [];

        function _() {
          x.push(k), k = !1
        }

        function E() {
          const e = x.pop();
          k = void 0 === e || e
        }

        function T(e) {
          const {
            cleanup: t
          } = e;
          if (e.cleanup = void 0, t) {
            const e = o;
            o = void 0;
            try {
              t()
            } finally {
              o = e
            }
          }
        }
        let C = 0;
        class O {
          constructor(e, t) {
            this.sub = e, this.dep = t, this.version = t.version, this.nextDep = this.prevDep = this.nextSub = this.prevSub = this.prevActiveLink = void 0
          }
        }
        class R {
          constructor(e) {
            this.computed = e, this.version = 0, this.activeLink = void 0, this.subs = void 0, this.map = void 0, this.key = void 0, this.sc = 0
          }
          track(e) {
            if (!o || !k || o === this.computed) return;
            let t = this.activeLink;
            if (void 0 === t || t.sub !== o) t = this.activeLink = new O(o, this), o.deps ? (t.prevDep = o.depsTail, o.depsTail.nextDep = t, o.depsTail = t) : o.deps = o.depsTail = t, F(t);
            else if (-1 === t.version && (t.version = this.version, t.nextDep)) {
              const e = t.nextDep;
              e.prevDep = t.prevDep, t.prevDep && (t.prevDep.nextDep = e), t.prevDep = o.depsTail, t.nextDep = void 0, o.depsTail.nextDep = t, o.depsTail = t, o.deps === t && (o.deps = e)
            }
            return t
          }
          trigger(e) {
            this.version++, C++, this.notify(e)
          }
          notify(e) {
            p();
            try {
              0;
              for (let e = this.subs; e; e = e.prevSub) e.sub.notify() && e.sub.dep.notify()
            } finally {
              m()
            }
          }
        }

        function F(e) {
          if (e.dep.sc++, 4 & e.sub.flags) {
            const t = e.dep.computed;
            if (t && !e.dep.subs) {
              t.flags |= 20;
              for (let e = t.deps; e; e = e.nextDep) F(e)
            }
            const n = e.dep.subs;
            n !== e && (e.prevSub = n, n && (n.nextSub = e)), e.dep.subs = e
          }
        }
        const L = new WeakMap,
          P = Symbol(""),
          N = Symbol(""),
          A = Symbol("");

        function M(e, t, n) {
          if (k && o) {
            let t = L.get(e);
            t || L.set(e, t = new Map);
            let r = t.get(n);
            r || (t.set(n, r = new R), r.map = t, r.key = n), r.track()
          }
        }

        function B(e, t, n, s, o, i) {
          const a = L.get(e);
          if (!a) return void C++;
          const l = e => {
            e && e.trigger()
          };
          if (p(), "clear" === t) a.forEach(l);
          else {
            const o = (0, r.cy)(e),
              i = o && (0, r.yI)(n);
            if (o && "length" === n) {
              const e = Number(s);
              a.forEach(((t, n) => {
                ("length" === n || n === A || !(0, r.Bm)(n) && n >= e) && l(t)
              }))
            } else switch ((void 0 !== n || a.has(void 0)) && l(a.get(n)), i && l(a.get(A)), t) {
              case "add":
                o ? i && l(a.get("length")) : (l(a.get(P)), (0, r.CE)(e) && l(a.get(N)));
                break;
              case "delete":
                o || (l(a.get(P)), (0, r.CE)(e) && l(a.get(N)));
                break;
              case "set":
                (0, r.CE)(e) && l(a.get(P));
                break
            }
          }
          m()
        }

        function j(e) {
          const t = _e(e);
          return t === e ? t : (M(t, "iterate", A), ke(e) ? t : t.map(Te))
        }

        function D(e) {
          return M(e = _e(e), "iterate", A), e
        }
        const I = {
          __proto__: null,
          [Symbol.iterator]() {
            return U(this, Symbol.iterator, Te)
          },
          concat(...e) {
            return j(this).concat(...e.map((e => (0, r.cy)(e) ? j(e) : e)))
          },
          entries() {
            return U(this, "entries", (e => (e[1] = Te(e[1]), e)))
          },
          every(e, t) {
            return $(this, "every", e, t, void 0, arguments)
          },
          filter(e, t) {
            return $(this, "filter", e, t, (e => e.map(Te)), arguments)
          },
          find(e, t) {
            return $(this, "find", e, t, Te, arguments)
          },
          findIndex(e, t) {
            return $(this, "findIndex", e, t, void 0, arguments)
          },
          findLast(e, t) {
            return $(this, "findLast", e, t, Te, arguments)
          },
          findLastIndex(e, t) {
            return $(this, "findLastIndex", e, t, void 0, arguments)
          },
          forEach(e, t) {
            return $(this, "forEach", e, t, void 0, arguments)
          },
          includes(...e) {
            return q(this, "includes", e)
          },
          indexOf(...e) {
            return q(this, "indexOf", e)
          },
          join(e) {
            return j(this).join(e)
          },
          lastIndexOf(...e) {
            return q(this, "lastIndexOf", e)
          },
          map(e, t) {
            return $(this, "map", e, t, void 0, arguments)
          },
          pop() {
            return X(this, "pop")
          },
          push(...e) {
            return X(this, "push", e)
          },
          reduce(e, ...t) {
            return W(this, "reduce", e, t)
          },
          reduceRight(e, ...t) {
            return W(this, "reduceRight", e, t)
          },
          shift() {
            return X(this, "shift")
          },
          some(e, t) {
            return $(this, "some", e, t, void 0, arguments)
          },
          splice(...e) {
            return X(this, "splice", e)
          },
          toReversed() {
            return j(this).toReversed()
          },
          toSorted(e) {
            return j(this).toSorted(e)
          },
          toSpliced(...e) {
            return j(this).toSpliced(...e)
          },
          unshift(...e) {
            return X(this, "unshift", e)
          },
          values() {
            return U(this, "values", Te)
          }
        };

        function U(e, t, n) {
          const r = D(e),
            s = r[t]();
          return r === e || ke(e) || (s._next = s.next, s.next = () => {
            const e = s._next();
            return e.value && (e.value = n(e.value)), e
          }), s
        }
        const V = Array.prototype;

        function $(e, t, n, r, s, o) {
          const i = D(e),
            a = i !== e && !ke(e),
            l = i[t];
          if (l !== V[t]) {
            const t = l.apply(e, o);
            return a ? Te(t) : t
          }
          let c = n;
          i !== e && (a ? c = function (t, r) {
            return n.call(this, Te(t), r, e)
          } : n.length > 2 && (c = function (t, r) {
            return n.call(this, t, r, e)
          }));
          const u = l.call(i, c, r);
          return a && s ? s(u) : u
        }

        function W(e, t, n, r) {
          const s = D(e);
          let o = n;
          return s !== e && (ke(e) ? n.length > 3 && (o = function (t, r, s) {
            return n.call(this, t, r, s, e)
          }) : o = function (t, r, s) {
            return n.call(this, t, Te(r), s, e)
          }), s[t](o, ...r)
        }

        function q(e, t, n) {
          const r = _e(e);
          M(r, "iterate", A);
          const s = r[t](...n);
          return -1 !== s && !1 !== s || !xe(n[0]) ? s : (n[0] = _e(n[0]), r[t](...n))
        }

        function X(e, t, n = []) {
          _(), p();
          const r = _e(e)[t].apply(e, n);
          return m(), E(), r
        }
        const K = (0, r.pD)("__proto__,__v_isRef,__isVue"),
          H = new Set(Object.getOwnPropertyNames(Symbol).filter((e => "arguments" !== e && "caller" !== e)).map((e => Symbol[e])).filter(r.Bm));

        function z(e) {
          (0, r.Bm)(e) || (e = String(e));
          const t = _e(this);
          return M(t, "has", e), t.hasOwnProperty(e)
        }
        class G {
          constructor(e = !1, t = !1) {
            this._isReadonly = e, this._isShallow = t
          }
          get(e, t, n) {
            const s = this._isReadonly,
              o = this._isShallow;
            if ("__v_isReactive" === t) return !s;
            if ("__v_isReadonly" === t) return s;
            if ("__v_isShallow" === t) return o;
            if ("__v_raw" === t) return n === (s ? o ? he : de : o ? fe : ue).get(e) || Object.getPrototypeOf(e) === Object.getPrototypeOf(n) ? e : void 0;
            const i = (0, r.cy)(e);
            if (!s) {
              let e;
              if (i && (e = I[t])) return e;
              if ("hasOwnProperty" === t) return z
            }
            const a = Reflect.get(e, t, Oe(e) ? e : n);
            return ((0, r.Bm)(t) ? H.has(t) : K(t)) ? a : (s || M(e, "get", t), o ? a : Oe(a) ? i && (0, r.yI)(t) ? a : a.value : (0, r.Gv)(a) ? s ? ye(a) : ge(a) : a)
          }
        }
        class Q extends G {
          constructor(e = !1) {
            super(!1, e)
          }
          set(e, t, n, s) {
            let o = e[t];
            if (!this._isShallow) {
              const t = we(o);
              if (ke(n) || we(n) || (o = _e(o), n = _e(n)), !(0, r.cy)(e) && Oe(o) && !Oe(n)) return !t && (o.value = n, !0)
            }
            const i = (0, r.cy)(e) && (0, r.yI)(t) ? Number(t) < e.length : (0, r.$3)(e, t),
              a = Reflect.set(e, t, n, Oe(e) ? e : s);
            return e === _e(s) && (i ? (0, r.$H)(n, o) && B(e, "set", t, n, o) : B(e, "add", t, n)), a
          }
          deleteProperty(e, t) {
            const n = (0, r.$3)(e, t),
              s = e[t],
              o = Reflect.deleteProperty(e, t);
            return o && n && B(e, "delete", t, void 0, s), o
          }
          has(e, t) {
            const n = Reflect.has(e, t);
            return (0, r.Bm)(t) && H.has(t) || M(e, "has", t), n
          }
          ownKeys(e) {
            return M(e, "iterate", (0, r.cy)(e) ? "length" : P), Reflect.ownKeys(e)
          }
        }
        class J extends G {
          constructor(e = !1) {
            super(!0, e)
          }
          set(e, t) {
            return !0
          }
          deleteProperty(e, t) {
            return !0
          }
        }
        const Z = new Q,
          Y = new J,
          ee = new Q(!0),
          te = e => e,
          ne = e => Reflect.getPrototypeOf(e);

        function re(e, t, n) {
          return function (...s) {
            const o = this["__v_raw"],
              i = _e(o),
              a = (0, r.CE)(i),
              l = "entries" === e || e === Symbol.iterator && a,
              c = "keys" === e && a,
              u = o[e](...s),
              f = n ? te : t ? Ce : Te;
            return !t && M(i, "iterate", c ? N : P), {
              next() {
                const {
                  value: e,
                  done: t
                } = u.next();
                return t ? {
                  value: e,
                  done: t
                } : {
                  value: l ? [f(e[0]), f(e[1])] : f(e),
                  done: t
                }
              },
              [Symbol.iterator]() {
                return this
              }
            }
          }
        }

        function se(e) {
          return function (...t) {
            return "delete" !== e && ("clear" === e ? void 0 : this)
          }
        }

        function oe(e, t) {
          const n = {
            get(n) {
              const s = this["__v_raw"],
                o = _e(s),
                i = _e(n);
              e || ((0, r.$H)(n, i) && M(o, "get", n), M(o, "get", i));
              const {
                has: a
              } = ne(o), l = t ? te : e ? Ce : Te;
              return a.call(o, n) ? l(s.get(n)) : a.call(o, i) ? l(s.get(i)) : void(s !== o && s.get(n))
            },
            get size() {
              const t = this["__v_raw"];
              return !e && M(_e(t), "iterate", P), Reflect.get(t, "size", t)
            },
            has(t) {
              const n = this["__v_raw"],
                s = _e(n),
                o = _e(t);
              return e || ((0, r.$H)(t, o) && M(s, "has", t), M(s, "has", o)), t === o ? n.has(t) : n.has(t) || n.has(o)
            },
            forEach(n, r) {
              const s = this,
                o = s["__v_raw"],
                i = _e(o),
                a = t ? te : e ? Ce : Te;
              return !e && M(i, "iterate", P), o.forEach(((e, t) => n.call(r, a(e), a(t), s)))
            }
          };
          (0, r.X$)(n, e ? {
            add: se("add"),
            set: se("set"),
            delete: se("delete"),
            clear: se("clear")
          } : {
            add(e) {
              t || ke(e) || we(e) || (e = _e(e));
              const n = _e(this),
                r = ne(n),
                s = r.has.call(n, e);
              return s || (n.add(e), B(n, "add", e, e)), this
            },
            set(e, n) {
              t || ke(n) || we(n) || (n = _e(n));
              const s = _e(this),
                {
                  has: o,
                  get: i
                } = ne(s);
              let a = o.call(s, e);
              a || (e = _e(e), a = o.call(s, e));
              const l = i.call(s, e);
              return s.set(e, n), a ? (0, r.$H)(n, l) && B(s, "set", e, n, l) : B(s, "add", e, n), this
            },
            delete(e) {
              const t = _e(this),
                {
                  has: n,
                  get: r
                } = ne(t);
              let s = n.call(t, e);
              s || (e = _e(e), s = n.call(t, e));
              const o = r ? r.call(t, e) : void 0,
                i = t.delete(e);
              return s && B(t, "delete", e, void 0, o), i
            },
            clear() {
              const e = _e(this),
                t = 0 !== e.size,
                n = void 0,
                r = e.clear();
              return t && B(e, "clear", void 0, void 0, n), r
            }
          });
          const s = ["keys", "values", "entries", Symbol.iterator];
          return s.forEach((r => {
            n[r] = re(r, e, t)
          })), n
        }

        function ie(e, t) {
          const n = oe(e, t);
          return (t, s, o) => "__v_isReactive" === s ? !e : "__v_isReadonly" === s ? e : "__v_raw" === s ? t : Reflect.get((0, r.$3)(n, s) && s in t ? n : t, s, o)
        }
        const ae = {
            get: ie(!1, !1)
          },
          le = {
            get: ie(!1, !0)
          },
          ce = {
            get: ie(!0, !1)
          };
        const ue = new WeakMap,
          fe = new WeakMap,
          de = new WeakMap,
          he = new WeakMap;

        function pe(e) {
          switch (e) {
            case "Object":
            case "Array":
              return 1;
            case "Map":
            case "Set":
            case "WeakMap":
            case "WeakSet":
              return 2;
            default:
              return 0
          }
        }

        function me(e) {
          return e["__v_skip"] || !Object.isExtensible(e) ? 0 : pe((0, r.Zf)(e))
        }

        function ge(e) {
          return we(e) ? e : be(e, !1, Z, ae, ue)
        }

        function ve(e) {
          return be(e, !1, ee, le, fe)
        }

        function ye(e) {
          return be(e, !0, Y, ce, de)
        }

        function be(e, t, n, s, o) {
          if (!(0, r.Gv)(e)) return e;
          if (e["__v_raw"] && (!t || !e["__v_isReactive"])) return e;
          const i = o.get(e);
          if (i) return i;
          const a = me(e);
          if (0 === a) return e;
          const l = new Proxy(e, 2 === a ? s : n);
          return o.set(e, l), l
        }

        function Se(e) {
          return we(e) ? Se(e["__v_raw"]) : !(!e || !e["__v_isReactive"])
        }

        function we(e) {
          return !(!e || !e["__v_isReadonly"])
        }

        function ke(e) {
          return !(!e || !e["__v_isShallow"])
        }

        function xe(e) {
          return !!e && !!e["__v_raw"]
        }

        function _e(e) {
          const t = e && e["__v_raw"];
          return t ? _e(t) : e
        }

        function Ee(e) {
          return !(0, r.$3)(e, "__v_skip") && Object.isExtensible(e) && (0, r.yQ)(e, "__v_skip", !0), e
        }
        const Te = e => (0, r.Gv)(e) ? ge(e) : e,
          Ce = e => (0, r.Gv)(e) ? ye(e) : e;

        function Oe(e) {
          return !!e && !0 === e["__v_isRef"]
        }

        function Re(e) {
          return Le(e, !1)
        }

        function Fe(e) {
          return Le(e, !0)
        }

        function Le(e, t) {
          return Oe(e) ? e : new Pe(e, t)
        }
        class Pe {
          constructor(e, t) {
            this.dep = new R, this["__v_isRef"] = !0, this["__v_isShallow"] = !1, this._rawValue = t ? e : _e(e), this._value = t ? e : Te(e), this["__v_isShallow"] = t
          }
          get value() {
            return this.dep.track(), this._value
          }
          set value(e) {
            const t = this._rawValue,
              n = this["__v_isShallow"] || ke(e) || we(e);
            e = n ? e : _e(e), (0, r.$H)(e, t) && (this._rawValue = e, this._value = n ? e : Te(e), this.dep.trigger())
          }
        }

        function Ne(e) {
          return Oe(e) ? e.value : e
        }
        const Ae = {
          get: (e, t, n) => "__v_raw" === t ? e : Ne(Reflect.get(e, t, n)),
          set: (e, t, n, r) => {
            const s = e[t];
            return Oe(s) && !Oe(n) ? (s.value = n, !0) : Reflect.set(e, t, n, r)
          }
        };

        function Me(e) {
          return Se(e) ? e : new Proxy(e, Ae)
        }
        class Be {
          constructor(e, t, n) {
            this.fn = e, this.setter = t, this._value = void 0, this.dep = new R(this), this.__v_isRef = !0, this.deps = void 0, this.depsTail = void 0, this.flags = 16, this.globalVersion = C - 1, this.next = void 0, this.effect = this, this["__v_isReadonly"] = !t, this.isSSR = n
          }
          notify() {
            if (this.flags |= 16, !(8 & this.flags || o === this)) return h(this, !0), !0
          }
          get value() {
            const e = this.dep.track();
            return b(this), e && (e.version = this.dep.version), this._value
          }
          set value(e) {
            this.setter && this.setter(e)
          }
        }

        function je(e, t, n = !1) {
          let s, o;
          (0, r.Tn)(e) ? s = e: (s = e.get, o = e.set);
          const i = new Be(s, o, n);
          return i
        }
        const De = {},
          Ie = new WeakMap;
        let Ue;

        function Ve(e, t = !1, n = Ue) {
          if (n) {
            let t = Ie.get(n);
            t || Ie.set(n, t = []), t.push(e)
          } else 0
        }

        function $e(e, t, n = r.MZ) {
          const {
            immediate: s,
            deep: o,
            once: i,
            scheduler: l,
            augmentJob: u,
            call: f
          } = n, d = e => o ? e : ke(e) || !1 === o || 0 === o ? We(e, 1) : We(e);
          let h, p, m, g, v = !1,
            y = !1;
          if (Oe(e) ? (p = () => e.value, v = ke(e)) : Se(e) ? (p = () => d(e), v = !0) : (0, r.cy)(e) ? (y = !0, v = e.some((e => Se(e) || ke(e))), p = () => e.map((e => Oe(e) ? e.value : Se(e) ? d(e) : (0, r.Tn)(e) ? f ? f(e, 2) : e() : void 0))) : p = (0, r.Tn)(e) ? t ? f ? () => f(e, 2) : e : () => {
              if (m) {
                _();
                try {
                  m()
                } finally {
                  E()
                }
              }
              const t = Ue;
              Ue = h;
              try {
                return f ? f(e, 3, [g]) : e(g)
              } finally {
                Ue = t
              }
            } : r.tE, t && o) {
            const e = p,
              t = !0 === o ? 1 / 0 : o;
            p = () => We(e(), t)
          }
          const b = a(),
            S = () => {
              h.stop(), b && (0, r.TF)(b.effects, h)
            };
          if (i && t) {
            const e = t;
            t = (...t) => {
              e(...t), S()
            }
          }
          let w = y ? new Array(e.length).fill(De) : De;
          const k = e => {
            if (1 & h.flags && (h.dirty || e))
              if (t) {
                const e = h.run();
                if (o || v || (y ? e.some(((e, t) => (0, r.$H)(e, w[t]))) : (0, r.$H)(e, w))) {
                  m && m();
                  const n = Ue;
                  Ue = h;
                  try {
                    const n = [e, w === De ? void 0 : y && w[0] === De ? [] : w, g];
                    f ? f(t, 3, n) : t(...n), w = e
                  } finally {
                    Ue = n
                  }
                }
              } else h.run()
          };
          return u && u(k), h = new c(p), h.scheduler = l ? () => l(k, !1) : k, g = e => Ve(e, !1, h), m = h.onStop = () => {
            const e = Ie.get(h);
            if (e) {
              if (f) f(e, 4);
              else
                for (const t of e) t();
              Ie.delete(h)
            }
          }, t ? s ? k(!0) : w = h.run() : l ? l(k.bind(null, !0), !0) : h.run(), S.pause = h.pause.bind(h), S.resume = h.resume.bind(h), S.stop = S, S
        }

        function We(e, t = 1 / 0, n) {
          if (t <= 0 || !(0, r.Gv)(e) || e["__v_skip"]) return e;
          if (n = n || new Set, n.has(e)) return e;
          if (n.add(e), t--, Oe(e)) We(e.value, t, n);
          else if ((0, r.cy)(e))
            for (let r = 0; r < e.length; r++) We(e[r], t, n);
          else if ((0, r.vM)(e) || (0, r.CE)(e)) e.forEach((e => {
            We(e, t, n)
          }));
          else if ((0, r.Qd)(e)) {
            for (const r in e) We(e[r], t, n);
            for (const r of Object.getOwnPropertySymbols(e)) Object.prototype.propertyIsEnumerable.call(e, r) && We(e[r], t, n)
          }
          return e
        }
      },
      641: function (e, t, n) {
        "use strict";
        n.d(t, {
          $u: function () {
            return oe
          },
          CE: function () {
            return zt
          },
          Df: function () {
            return V
          },
          EW: function () {
            return Bn
          },
          FK: function () {
            return jt
          },
          Fv: function () {
            return an
          },
          Gt: function () {
            return $e
          },
          Gy: function () {
            return M
          },
          K9: function () {
            return ut
          },
          Lk: function () {
            return en
          },
          MZ: function () {
            return U
          },
          OW: function () {
            return I
          },
          Q3: function () {
            return ln
          },
          QP: function () {
            return j
          },
          WQ: function () {
            return We
          },
          Wv: function () {
            return Gt
          },
          bF: function () {
            return tn
          },
          bo: function () {
            return R
          },
          dY: function () {
            return g
          },
          eW: function () {
            return on
          },
          g2: function () {
            return he
          },
          h: function () {
            return jn
          },
          nI: function () {
            return yn
          },
          pI: function () {
            return ve
          },
          pM: function () {
            return $
          },
          qL: function () {
            return i
          },
          uX: function () {
            return Wt
          },
          wB: function () {
            return wt
          }
        });
        var r = n(953),
          s = n(33);

        function o(e, t, n, r) {
          try {
            return r ? e(...r) : e()
          } catch (s) {
            a(s, t, n)
          }
        }

        function i(e, t, n, r) {
          if ((0, s.Tn)(e)) {
            const i = o(e, t, n, r);
            return i && (0, s.yL)(i) && i.catch((e => {
              a(e, t, n)
            })), i
          }
          if ((0, s.cy)(e)) {
            const s = [];
            for (let o = 0; o < e.length; o++) s.push(i(e[o], t, n, r));
            return s
          }
        }

        function a(e, t, n, i = !0) {
          const a = t ? t.vnode : null,
            {
              errorHandler: c,
              throwUnhandledErrorInProduction: u
            } = t && t.appContext.config || s.MZ;
          if (t) {
            let s = t.parent;
            const i = t.proxy,
              a = `https://vuejs.org/error-reference/#runtime-${n}`;
            while (s) {
              const t = s.ec;
              if (t)
                for (let n = 0; n < t.length; n++)
                  if (!1 === t[n](e, i, a)) return;
              s = s.parent
            }
            if (c) return (0, r.C4)(), o(c, null, 10, [e, i, a]), void(0, r.bl)()
          }
          l(e, n, a, i, u)
        }

        function l(e, t, n, r = !0, s = !1) {
          if (s) throw e;
          console.error(e)
        }
        const c = [];
        let u = -1;
        const f = [];
        let d = null,
          h = 0;
        const p = Promise.resolve();
        let m = null;

        function g(e) {
          const t = m || p;
          return e ? t.then(this ? e.bind(this) : e) : t
        }

        function v(e) {
          let t = u + 1,
            n = c.length;
          while (t < n) {
            const r = t + n >>> 1,
              s = c[r],
              o = x(s);
            o < e || o === e && 2 & s.flags ? t = r + 1 : n = r
          }
          return t
        }

        function y(e) {
          if (!(1 & e.flags)) {
            const t = x(e),
              n = c[c.length - 1];
            !n || !(2 & e.flags) && t >= x(n) ? c.push(e) : c.splice(v(t), 0, e), e.flags |= 1, b()
          }
        }

        function b() {
          m || (m = p.then(_))
        }

        function S(e) {
          (0, s.cy)(e) ? f.push(...e): d && -1 === e.id ? d.splice(h + 1, 0, e) : 1 & e.flags || (f.push(e), e.flags |= 1), b()
        }

        function w(e, t, n = u + 1) {
          for (0; n < c.length; n++) {
            const t = c[n];
            if (t && 2 & t.flags) {
              if (e && t.id !== e.uid) continue;
              0, c.splice(n, 1), n--, 4 & t.flags && (t.flags &= -2), t(), 4 & t.flags || (t.flags &= -2)
            }
          }
        }

        function k(e) {
          if (f.length) {
            const e = [...new Set(f)].sort(((e, t) => x(e) - x(t)));
            if (f.length = 0, d) return void d.push(...e);
            for (d = e, h = 0; h < d.length; h++) {
              const e = d[h];
              0, 4 & e.flags && (e.flags &= -2), 8 & e.flags || e(), e.flags &= -2
            }
            d = null, h = 0
          }
        }
        const x = e => null == e.id ? 2 & e.flags ? -1 : 1 / 0 : e.id;

        function _(e) {
          s.tE;
          try {
            for (u = 0; u < c.length; u++) {
              const e = c[u];
              !e || 8 & e.flags || (4 & e.flags && (e.flags &= -2), o(e, e.i, e.i ? 15 : 14), 4 & e.flags || (e.flags &= -2))
            }
          } finally {
            for (; u < c.length; u++) {
              const e = c[u];
              e && (e.flags &= -2)
            }
            u = -1, c.length = 0, k(e), m = null, (c.length || f.length) && _(e)
          }
        }
        let E = null,
          T = null;

        function C(e) {
          const t = E;
          return E = e, T = e && e.type.__scopeId || null, t
        }

        function O(e, t = E, n) {
          if (!t) return e;
          if (e._n) return e;
          const r = (...n) => {
            r._d && Kt(-1);
            const s = C(t);
            let o;
            try {
              o = e(...n)
            } finally {
              C(s), r._d && Kt(1)
            }
            return o
          };
          return r._n = !0, r._c = !0, r._d = !0, r
        }

        function R(e, t) {
          if (null === E) return e;
          const n = Nn(E),
            o = e.dirs || (e.dirs = []);
          for (let i = 0; i < t.length; i++) {
            let [e, a, l, c = s.MZ] = t[i];
            e && ((0, s.Tn)(e) && (e = {
              mounted: e,
              updated: e
            }), e.deep && (0, r.hV)(a), o.push({
              dir: e,
              instance: n,
              value: a,
              oldValue: void 0,
              arg: l,
              modifiers: c
            }))
          }
          return e
        }

        function F(e, t, n, s) {
          const o = e.dirs,
            a = t && t.dirs;
          for (let l = 0; l < o.length; l++) {
            const c = o[l];
            a && (c.oldValue = a[l].value);
            let u = c.dir[s];
            u && ((0, r.C4)(), i(u, n, 8, [e.el, c, e, t]), (0, r.bl)())
          }
        }
        const L = Symbol("_vte"),
          P = e => e.__isTeleport;
        const N = Symbol("_leaveCb"),
          A = Symbol("_enterCb");

        function M() {
          const e = {
            isMounted: !1,
            isLeaving: !1,
            isUnmounting: !1,
            leavingVNodes: new Map
          };
          return re((() => {
            e.isMounted = !0
          })), ie((() => {
            e.isUnmounting = !0
          })), e
        }
        const B = [Function, Array],
          j = {
            mode: String,
            appear: Boolean,
            persisted: Boolean,
            onBeforeEnter: B,
            onEnter: B,
            onAfterEnter: B,
            onEnterCancelled: B,
            onBeforeLeave: B,
            onLeave: B,
            onAfterLeave: B,
            onLeaveCancelled: B,
            onBeforeAppear: B,
            onAppear: B,
            onAfterAppear: B,
            onAppearCancelled: B
          };

        function D(e, t) {
          const {
            leavingVNodes: n
          } = e;
          let r = n.get(t.type);
          return r || (r = Object.create(null), n.set(t.type, r)), r
        }

        function I(e, t, n, r, o) {
          const {
            appear: a,
            mode: l,
            persisted: c = !1,
            onBeforeEnter: u,
            onEnter: f,
            onAfterEnter: d,
            onEnterCancelled: h,
            onBeforeLeave: p,
            onLeave: m,
            onAfterLeave: g,
            onLeaveCancelled: v,
            onBeforeAppear: y,
            onAppear: b,
            onAfterAppear: S,
            onAppearCancelled: w
          } = t, k = String(e.key), x = D(n, e), _ = (e, t) => {
            e && i(e, r, 9, t)
          }, E = (e, t) => {
            const n = t[1];
            _(e, t), (0, s.cy)(e) ? e.every((e => e.length <= 1)) && n() : e.length <= 1 && n()
          }, T = {
            mode: l,
            persisted: c,
            beforeEnter(t) {
              let r = u;
              if (!n.isMounted) {
                if (!a) return;
                r = y || u
              }
              t[N] && t[N](!0);
              const s = x[k];
              s && Jt(e, s) && s.el[N] && s.el[N](), _(r, [t])
            },
            enter(e) {
              let t = f,
                r = d,
                s = h;
              if (!n.isMounted) {
                if (!a) return;
                t = b || f, r = S || d, s = w || h
              }
              let o = !1;
              const i = e[A] = t => {
                o || (o = !0, _(t ? s : r, [e]), T.delayedLeave && T.delayedLeave(), e[A] = void 0)
              };
              t ? E(t, [e, i]) : i()
            },
            leave(t, r) {
              const s = String(e.key);
              if (t[A] && t[A](!0), n.isUnmounting) return r();
              _(p, [t]);
              let o = !1;
              const i = t[N] = n => {
                o || (o = !0, r(), _(n ? v : g, [t]), t[N] = void 0, x[s] === e && delete x[s])
              };
              x[s] = e, m ? E(m, [t, i]) : i()
            },
            clone(e) {
              const s = I(e, t, n, r, o);
              return o && o(s), s
            }
          };
          return T
        }

        function U(e, t) {
          6 & e.shapeFlag && e.component ? (e.transition = t, U(e.component.subTree, t)) : 128 & e.shapeFlag ? (e.ssContent.transition = t.clone(e.ssContent), e.ssFallback.transition = t.clone(e.ssFallback)) : e.transition = t
        }

        function V(e, t = !1, n) {
          let r = [],
            s = 0;
          for (let o = 0; o < e.length; o++) {
            let i = e[o];
            const a = null == n ? i.key : String(n) + String(null != i.key ? i.key : o);
            i.type === jt ? (128 & i.patchFlag && s++, r = r.concat(V(i.children, t, a))) : (t || i.type !== It) && r.push(null != a ? sn(i, {
              key: a
            }) : i)
          }
          if (s > 1)
            for (let o = 0; o < r.length; o++) r[o].patchFlag = -2;
          return r
        }
        /*! #__NO_SIDE_EFFECTS__ */
        function $(e, t) {
          return (0, s.Tn)(e) ? (() => (0, s.X$)({
            name: e.name
          }, t, {
            setup: e
          }))() : e
        }

        function W(e) {
          e.ids = [e.ids[0] + e.ids[2]++ + "-", 0, 0]
        }

        function q(e, t, n, i, a = !1) {
          if ((0, s.cy)(e)) return void e.forEach(((e, r) => q(e, t && ((0, s.cy)(t) ? t[r] : t), n, i, a)));
          if (X(i) && !a) return;
          const l = 4 & i.shapeFlag ? Nn(i.component) : i.el,
            c = a ? null : l,
            {
              i: u,
              r: f
            } = e;
          const d = t && t.r,
            h = u.refs === s.MZ ? u.refs = {} : u.refs,
            p = u.setupState,
            m = (0, r.ux)(p),
            g = p === s.MZ ? () => !1 : e => (0, s.$3)(m, e);
          if (null != d && d !== f && ((0, s.Kg)(d) ? (h[d] = null, g(d) && (p[d] = null)) : (0, r.i9)(d) && (d.value = null)), (0, s.Tn)(f)) o(f, u, 12, [c, h]);
          else {
            const t = (0, s.Kg)(f),
              o = (0, r.i9)(f);
            if (t || o) {
              const r = () => {
                if (e.f) {
                  const n = t ? g(f) ? p[f] : h[f] : f.value;
                  a ? (0, s.cy)(n) && (0, s.TF)(n, l) : (0, s.cy)(n) ? n.includes(l) || n.push(l) : t ? (h[f] = [l], g(f) && (p[f] = h[f])) : (f.value = [l], e.k && (h[e.k] = f.value))
                } else t ? (h[f] = c, g(f) && (p[f] = c)) : o && (f.value = c, e.k && (h[e.k] = c))
              };
              c ? (r.id = -1, ct(r, n)) : r()
            } else 0
          }
        }(0, s.We)().requestIdleCallback, (0, s.We)().cancelIdleCallback;
        const X = e => !!e.type.__asyncLoader
        /*! #__NO_SIDE_EFFECTS__ */
        ;
        const K = e => e.type.__isKeepAlive;
        RegExp, RegExp;

        function H(e, t) {
          return (0, s.cy)(e) ? e.some((e => H(e, t))) : (0, s.Kg)(e) ? e.split(",").includes(t) : !!(0, s.gd)(e) && (e.lastIndex = 0, e.test(t))
        }

        function z(e, t) {
          Q(e, "a", t)
        }

        function G(e, t) {
          Q(e, "da", t)
        }

        function Q(e, t, n = vn) {
          const r = e.__wdc || (e.__wdc = () => {
            let t = n;
            while (t) {
              if (t.isDeactivated) return;
              t = t.parent
            }
            return e()
          });
          if (ee(t, r, n), n) {
            let e = n.parent;
            while (e && e.parent) K(e.parent.vnode) && J(r, t, n, e), e = e.parent
          }
        }

        function J(e, t, n, r) {
          const o = ee(t, e, r, !0);
          ae((() => {
            (0, s.TF)(r[t], o)
          }), n)
        }

        function Z(e) {
          e.shapeFlag &= -257, e.shapeFlag &= -513
        }

        function Y(e) {
          return 128 & e.shapeFlag ? e.ssContent : e
        }

        function ee(e, t, n = vn, s = !1) {
          if (n) {
            const o = n[e] || (n[e] = []),
              a = t.__weh || (t.__weh = (...s) => {
                (0, r.C4)();
                const o = wn(n),
                  a = i(t, n, e, s);
                return o(), (0, r.bl)(), a
              });
            return s ? o.unshift(a) : o.push(a), a
          }
        }
        const te = e => (t, n = vn) => {
            Tn && "sp" !== e || ee(e, ((...e) => t(...e)), n)
          },
          ne = te("bm"),
          re = te("m"),
          se = te("bu"),
          oe = te("u"),
          ie = te("bum"),
          ae = te("um"),
          le = te("sp"),
          ce = te("rtg"),
          ue = te("rtc");

        function fe(e, t = vn) {
          ee("ec", e, t)
        }
        const de = "components";

        function he(e, t) {
          return me(de, e, !0, t) || e
        }
        const pe = Symbol.for("v-ndc");

        function me(e, t, n = !0, r = !1) {
          const o = E || vn;
          if (o) {
            const n = o.type;
            if (e === de) {
              const e = An(n, !1);
              if (e && (e === t || e === (0, s.PT)(t) || e === (0, s.ZH)((0, s.PT)(t)))) return n
            }
            const i = ge(o[e] || n[e], t) || ge(o.appContext[e], t);
            return !i && r ? n : i
          }
        }

        function ge(e, t) {
          return e && (e[t] || e[(0, s.PT)(t)] || e[(0, s.ZH)((0, s.PT)(t))])
        }

        function ve(e, t, n, o) {
          let i;
          const a = n && n[o],
            l = (0, s.cy)(e);
          if (l || (0, s.Kg)(e)) {
            const n = l && (0, r.g8)(e);
            let s = !1;
            n && (s = !(0, r.fE)(e), e = (0, r.qA)(e)), i = new Array(e.length);
            for (let o = 0, l = e.length; o < l; o++) i[o] = t(s ? (0, r.lJ)(e[o]) : e[o], o, void 0, a && a[o])
          } else if ("number" === typeof e) {
            0,
            i = new Array(e);
            for (let n = 0; n < e; n++) i[n] = t(n + 1, n, void 0, a && a[n])
          }
          else if ((0, s.Gv)(e))
            if (e[Symbol.iterator]) i = Array.from(e, ((e, n) => t(e, n, void 0, a && a[n])));
            else {
              const n = Object.keys(e);
              i = new Array(n.length);
              for (let r = 0, s = n.length; r < s; r++) {
                const s = n[r];
                i[r] = t(e[s], s, r, a && a[r])
              }
            }
          else i = [];
          return n && (n[o] = i), i
        }
        const ye = e => e ? xn(e) ? Nn(e) : ye(e.parent) : null,
          be = (0, s.X$)(Object.create(null), {
            $: e => e,
            $el: e => e.vnode.el,
            $data: e => e.data,
            $props: e => e.props,
            $attrs: e => e.attrs,
            $slots: e => e.slots,
            $refs: e => e.refs,
            $parent: e => ye(e.parent),
            $root: e => ye(e.root),
            $host: e => e.ce,
            $emit: e => e.emit,
            $options: e => Oe(e),
            $forceUpdate: e => e.f || (e.f = () => {
              y(e.update)
            }),
            $nextTick: e => e.n || (e.n = g.bind(e.proxy)),
            $watch: e => xt.bind(e)
          }),
          Se = (e, t) => e !== s.MZ && !e.__isScriptSetup && (0, s.$3)(e, t),
          we = {
            get({
              _: e
            }, t) {
              if ("__v_skip" === t) return !0;
              const {
                ctx: n,
                setupState: o,
                data: i,
                props: a,
                accessCache: l,
                type: c,
                appContext: u
              } = e;
              let f;
              if ("$" !== t[0]) {
                const r = l[t];
                if (void 0 !== r) switch (r) {
                  case 1:
                    return o[t];
                  case 2:
                    return i[t];
                  case 4:
                    return n[t];
                  case 3:
                    return a[t]
                } else {
                  if (Se(o, t)) return l[t] = 1, o[t];
                  if (i !== s.MZ && (0, s.$3)(i, t)) return l[t] = 2, i[t];
                  if ((f = e.propsOptions[0]) && (0, s.$3)(f, t)) return l[t] = 3, a[t];
                  if (n !== s.MZ && (0, s.$3)(n, t)) return l[t] = 4, n[t];
                  xe && (l[t] = 0)
                }
              }
              const d = be[t];
              let h, p;
              return d ? ("$attrs" === t && (0, r.u4)(e.attrs, "get", ""), d(e)) : (h = c.__cssModules) && (h = h[t]) ? h : n !== s.MZ && (0, s.$3)(n, t) ? (l[t] = 4, n[t]) : (p = u.config.globalProperties, (0, s.$3)(p, t) ? p[t] : void 0)
            },
            set({
              _: e
            }, t, n) {
              const {
                data: r,
                setupState: o,
                ctx: i
              } = e;
              return Se(o, t) ? (o[t] = n, !0) : r !== s.MZ && (0, s.$3)(r, t) ? (r[t] = n, !0) : !(0, s.$3)(e.props, t) && (("$" !== t[0] || !(t.slice(1) in e)) && (i[t] = n, !0))
            },
            has({
              _: {
                data: e,
                setupState: t,
                accessCache: n,
                ctx: r,
                appContext: o,
                propsOptions: i
              }
            }, a) {
              let l;
              return !!n[a] || e !== s.MZ && (0, s.$3)(e, a) || Se(t, a) || (l = i[0]) && (0, s.$3)(l, a) || (0, s.$3)(r, a) || (0, s.$3)(be, a) || (0, s.$3)(o.config.globalProperties, a)
            },
            defineProperty(e, t, n) {
              return null != n.get ? e._.accessCache[t] = 0 : (0, s.$3)(n, "value") && this.set(e, t, n.value, null), Reflect.defineProperty(e, t, n)
            }
          };

        function ke(e) {
          return (0, s.cy)(e) ? e.reduce(((e, t) => (e[t] = null, e)), {}) : e
        }
        let xe = !0;

        function _e(e) {
          const t = Oe(e),
            n = e.proxy,
            o = e.ctx;
          xe = !1, t.beforeCreate && Te(t.beforeCreate, e, "bc");
          const {
            data: i,
            computed: a,
            methods: l,
            watch: c,
            provide: u,
            inject: f,
            created: d,
            beforeMount: h,
            mounted: p,
            beforeUpdate: m,
            updated: g,
            activated: v,
            deactivated: y,
            beforeDestroy: b,
            beforeUnmount: S,
            destroyed: w,
            unmounted: k,
            render: x,
            renderTracked: _,
            renderTriggered: E,
            errorCaptured: T,
            serverPrefetch: C,
            expose: O,
            inheritAttrs: R,
            components: F,
            directives: L,
            filters: P
          } = t, N = null;
          if (f && Ee(f, o, N), l)
            for (const r in l) {
              const e = l[r];
              (0, s.Tn)(e) && (o[r] = e.bind(n))
            }
          if (i) {
            0;
            const t = i.call(n, n);
            0, (0, s.Gv)(t) && (e.data = (0, r.Kh)(t))
          }
          if (xe = !0, a)
            for (const r in a) {
              const e = a[r],
                t = (0, s.Tn)(e) ? e.bind(n, n) : (0, s.Tn)(e.get) ? e.get.bind(n, n) : s.tE;
              0;
              const i = !(0, s.Tn)(e) && (0, s.Tn)(e.set) ? e.set.bind(n) : s.tE,
                l = Bn({
                  get: t,
                  set: i
                });
              Object.defineProperty(o, r, {
                enumerable: !0,
                configurable: !0,
                get: () => l.value,
                set: e => l.value = e
              })
            }
          if (c)
            for (const r in c) Ce(c[r], o, n, r);
          if (u) {
            const e = (0, s.Tn)(u) ? u.call(n) : u;
            Reflect.ownKeys(e).forEach((t => {
              $e(t, e[t])
            }))
          }

          function A(e, t) {
            (0, s.cy)(t) ? t.forEach((t => e(t.bind(n)))): t && e(t.bind(n))
          }
          if (d && Te(d, e, "c"), A(ne, h), A(re, p), A(se, m), A(oe, g), A(z, v), A(G, y), A(fe, T), A(ue, _), A(ce, E), A(ie, S), A(ae, k), A(le, C), (0, s.cy)(O))
            if (O.length) {
              const t = e.exposed || (e.exposed = {});
              O.forEach((e => {
                Object.defineProperty(t, e, {
                  get: () => n[e],
                  set: t => n[e] = t
                })
              }))
            } else e.exposed || (e.exposed = {});
          x && e.render === s.tE && (e.render = x), null != R && (e.inheritAttrs = R), F && (e.components = F), L && (e.directives = L), C && W(e)
        }

        function Ee(e, t, n = s.tE) {
          (0, s.cy)(e) && (e = Ne(e));
          for (const o in e) {
            const n = e[o];
            let i;
            i = (0, s.Gv)(n) ? "default" in n ? We(n.from || o, n.default, !0) : We(n.from || o) : We(n), (0, r.i9)(i) ? Object.defineProperty(t, o, {
              enumerable: !0,
              configurable: !0,
              get: () => i.value,
              set: e => i.value = e
            }) : t[o] = i
          }
        }

        function Te(e, t, n) {
          i((0, s.cy)(e) ? e.map((e => e.bind(t.proxy))) : e.bind(t.proxy), t, n)
        }

        function Ce(e, t, n, r) {
          let o = r.includes(".") ? _t(n, r) : () => n[r];
          if ((0, s.Kg)(e)) {
            const n = t[e];
            (0, s.Tn)(n) && wt(o, n)
          } else if ((0, s.Tn)(e)) wt(o, e.bind(n));
          else if ((0, s.Gv)(e))
            if ((0, s.cy)(e)) e.forEach((e => Ce(e, t, n, r)));
            else {
              const r = (0, s.Tn)(e.handler) ? e.handler.bind(n) : t[e.handler];
              (0, s.Tn)(r) && wt(o, r, e)
            }
          else 0
        }

        function Oe(e) {
          const t = e.type,
            {
              mixins: n,
              extends: r
            } = t,
            {
              mixins: o,
              optionsCache: i,
              config: {
                optionMergeStrategies: a
              }
            } = e.appContext,
            l = i.get(t);
          let c;
          return l ? c = l : o.length || n || r ? (c = {}, o.length && o.forEach((e => Re(c, e, a, !0))), Re(c, t, a)) : c = t, (0, s.Gv)(t) && i.set(t, c), c
        }

        function Re(e, t, n, r = !1) {
          const {
            mixins: s,
            extends: o
          } = t;
          o && Re(e, o, n, !0), s && s.forEach((t => Re(e, t, n, !0)));
          for (const i in t)
            if (r && "expose" === i);
            else {
              const r = Fe[i] || n && n[i];
              e[i] = r ? r(e[i], t[i]) : t[i]
            } return e
        }
        const Fe = {
          data: Le,
          props: Be,
          emits: Be,
          methods: Me,
          computed: Me,
          beforeCreate: Ae,
          created: Ae,
          beforeMount: Ae,
          mounted: Ae,
          beforeUpdate: Ae,
          updated: Ae,
          beforeDestroy: Ae,
          beforeUnmount: Ae,
          destroyed: Ae,
          unmounted: Ae,
          activated: Ae,
          deactivated: Ae,
          errorCaptured: Ae,
          serverPrefetch: Ae,
          components: Me,
          directives: Me,
          watch: je,
          provide: Le,
          inject: Pe
        };

        function Le(e, t) {
          return t ? e ? function () {
            return (0, s.X$)((0, s.Tn)(e) ? e.call(this, this) : e, (0, s.Tn)(t) ? t.call(this, this) : t)
          } : t : e
        }

        function Pe(e, t) {
          return Me(Ne(e), Ne(t))
        }

        function Ne(e) {
          if ((0, s.cy)(e)) {
            const t = {};
            for (let n = 0; n < e.length; n++) t[e[n]] = e[n];
            return t
          }
          return e
        }

        function Ae(e, t) {
          return e ? [...new Set([].concat(e, t))] : t
        }

        function Me(e, t) {
          return e ? (0, s.X$)(Object.create(null), e, t) : t
        }

        function Be(e, t) {
          return e ? (0, s.cy)(e) && (0, s.cy)(t) ? [...new Set([...e, ...t])] : (0, s.X$)(Object.create(null), ke(e), ke(null != t ? t : {})) : t
        }

        function je(e, t) {
          if (!e) return t;
          if (!t) return e;
          const n = (0, s.X$)(Object.create(null), e);
          for (const r in t) n[r] = Ae(e[r], t[r]);
          return n
        }

        function De() {
          return {
            app: null,
            config: {
              isNativeTag: s.NO,
              performance: !1,
              globalProperties: {},
              optionMergeStrategies: {},
              errorHandler: void 0,
              warnHandler: void 0,
              compilerOptions: {}
            },
            mixins: [],
            components: {},
            directives: {},
            provides: Object.create(null),
            optionsCache: new WeakMap,
            propsCache: new WeakMap,
            emitsCache: new WeakMap
          }
        }
        let Ie = 0;

        function Ue(e, t) {
          return function (n, r = null) {
            (0, s.Tn)(n) || (n = (0, s.X$)({}, n)), null == r || (0, s.Gv)(r) || (r = null);
            const o = De(),
              a = new WeakSet,
              l = [];
            let c = !1;
            const u = o.app = {
              _uid: Ie++,
              _component: n,
              _props: r,
              _container: null,
              _context: o,
              _instance: null,
              version: Dn,
              get config() {
                return o.config
              },
              set config(e) {
                0
              },
              use(e, ...t) {
                return a.has(e) || (e && (0, s.Tn)(e.install) ? (a.add(e), e.install(u, ...t)) : (0, s.Tn)(e) && (a.add(e), e(u, ...t))), u
              },
              mixin(e) {
                return o.mixins.includes(e) || o.mixins.push(e), u
              },
              component(e, t) {
                return t ? (o.components[e] = t, u) : o.components[e]
              },
              directive(e, t) {
                return t ? (o.directives[e] = t, u) : o.directives[e]
              },
              mount(s, i, a) {
                if (!c) {
                  0;
                  const l = u._ceVNode || tn(n, r);
                  return l.appContext = o, !0 === a ? a = "svg" : !1 === a && (a = void 0), i && t ? t(l, s) : e(l, s, a), c = !0, u._container = s, s.__vue_app__ = u, Nn(l.component)
                }
              },
              onUnmount(e) {
                l.push(e)
              },
              unmount() {
                c && (i(l, u._instance, 16), e(null, u._container), delete u._container.__vue_app__)
              },
              provide(e, t) {
                return o.provides[e] = t, u
              },
              runWithContext(e) {
                const t = Ve;
                Ve = u;
                try {
                  return e()
                } finally {
                  Ve = t
                }
              }
            };
            return u
          }
        }
        let Ve = null;

        function $e(e, t) {
          if (vn) {
            let n = vn.provides;
            const r = vn.parent && vn.parent.provides;
            r === n && (n = vn.provides = Object.create(r)), n[e] = t
          } else 0
        }

        function We(e, t, n = !1) {
          const r = vn || E;
          if (r || Ve) {
            const o = Ve ? Ve._context.provides : r ? null == r.parent ? r.vnode.appContext && r.vnode.appContext.provides : r.parent.provides : void 0;
            if (o && e in o) return o[e];
            if (arguments.length > 1) return n && (0, s.Tn)(t) ? t.call(r && r.proxy) : t
          } else 0
        }
        const qe = {},
          Xe = () => Object.create(qe),
          Ke = e => Object.getPrototypeOf(e) === qe;

        function He(e, t, n, s = !1) {
          const o = {},
            i = Xe();
          e.propsDefaults = Object.create(null), Ge(e, t, o, i);
          for (const r in e.propsOptions[0]) r in o || (o[r] = void 0);
          n ? e.props = s ? o : (0, r.Gc)(o) : e.type.props ? e.props = o : e.props = i, e.attrs = i
        }

        function ze(e, t, n, o) {
          const {
            props: i,
            attrs: a,
            vnode: {
              patchFlag: l
            }
          } = e, c = (0, r.ux)(i), [u] = e.propsOptions;
          let f = !1;
          if (!(o || l > 0) || 16 & l) {
            let r;
            Ge(e, t, i, a) && (f = !0);
            for (const o in c) t && ((0, s.$3)(t, o) || (r = (0, s.Tg)(o)) !== o && (0, s.$3)(t, r)) || (u ? !n || void 0 === n[o] && void 0 === n[r] || (i[o] = Qe(u, c, o, void 0, e, !0)) : delete i[o]);
            if (a !== c)
              for (const e in a) t && (0, s.$3)(t, e) || (delete a[e], f = !0)
          } else if (8 & l) {
            const n = e.vnode.dynamicProps;
            for (let r = 0; r < n.length; r++) {
              let o = n[r];
              if (Ot(e.emitsOptions, o)) continue;
              const l = t[o];
              if (u)
                if ((0, s.$3)(a, o)) l !== a[o] && (a[o] = l, f = !0);
                else {
                  const t = (0, s.PT)(o);
                  i[t] = Qe(u, c, t, l, e, !1)
                }
              else l !== a[o] && (a[o] = l, f = !0)
            }
          }
          f && (0, r.hZ)(e.attrs, "set", "")
        }

        function Ge(e, t, n, o) {
          const [i, a] = e.propsOptions;
          let l, c = !1;
          if (t)
            for (let r in t) {
              if ((0, s.SU)(r)) continue;
              const u = t[r];
              let f;
              i && (0, s.$3)(i, f = (0, s.PT)(r)) ? a && a.includes(f) ? (l || (l = {}))[f] = u : n[f] = u : Ot(e.emitsOptions, r) || r in o && u === o[r] || (o[r] = u, c = !0)
            }
          if (a) {
            const t = (0, r.ux)(n),
              o = l || s.MZ;
            for (let r = 0; r < a.length; r++) {
              const l = a[r];
              n[l] = Qe(i, t, l, o[l], e, !(0, s.$3)(o, l))
            }
          }
          return c
        }

        function Qe(e, t, n, r, o, i) {
          const a = e[n];
          if (null != a) {
            const e = (0, s.$3)(a, "default");
            if (e && void 0 === r) {
              const e = a.default;
              if (a.type !== Function && !a.skipFactory && (0, s.Tn)(e)) {
                const {
                  propsDefaults: s
                } = o;
                if (n in s) r = s[n];
                else {
                  const i = wn(o);
                  r = s[n] = e.call(null, t), i()
                }
              } else r = e;
              o.ce && o.ce._setProp(n, r)
            }
            a[0] && (i && !e ? r = !1 : !a[1] || "" !== r && r !== (0, s.Tg)(n) || (r = !0))
          }
          return r
        }
        const Je = new WeakMap;

        function Ze(e, t, n = !1) {
          const r = n ? Je : t.propsCache,
            o = r.get(e);
          if (o) return o;
          const i = e.props,
            a = {},
            l = [];
          let c = !1;
          if (!(0, s.Tn)(e)) {
            const r = e => {
              c = !0;
              const [n, r] = Ze(e, t, !0);
              (0, s.X$)(a, n), r && l.push(...r)
            };
            !n && t.mixins.length && t.mixins.forEach(r), e.extends && r(e.extends), e.mixins && e.mixins.forEach(r)
          }
          if (!i && !c) return (0, s.Gv)(e) && r.set(e, s.Oj), s.Oj;
          if ((0, s.cy)(i))
            for (let f = 0; f < i.length; f++) {
              0;
              const e = (0, s.PT)(i[f]);
              Ye(e) && (a[e] = s.MZ)
            } else if (i) {
              0;
              for (const e in i) {
                const t = (0, s.PT)(e);
                if (Ye(t)) {
                  const n = i[e],
                    r = a[t] = (0, s.cy)(n) || (0, s.Tn)(n) ? {
                      type: n
                    } : (0, s.X$)({}, n),
                    o = r.type;
                  let c = !1,
                    u = !0;
                  if ((0, s.cy)(o))
                    for (let e = 0; e < o.length; ++e) {
                      const t = o[e],
                        n = (0, s.Tn)(t) && t.name;
                      if ("Boolean" === n) {
                        c = !0;
                        break
                      }
                      "String" === n && (u = !1)
                    } else c = (0, s.Tn)(o) && "Boolean" === o.name;
                  r[0] = c, r[1] = u, (c || (0, s.$3)(r, "default")) && l.push(t)
                }
              }
            } const u = [a, l];
          return (0, s.Gv)(e) && r.set(e, u), u
        }

        function Ye(e) {
          return "$" !== e[0] && !(0, s.SU)(e)
        }
        const et = e => "_" === e[0] || "$stable" === e,
          tt = e => (0, s.cy)(e) ? e.map(cn) : [cn(e)],
          nt = (e, t, n) => {
            if (t._n) return t;
            const r = O(((...e) => tt(t(...e))), n);
            return r._c = !1, r
          },
          rt = (e, t, n) => {
            const r = e._ctx;
            for (const o in e) {
              if (et(o)) continue;
              const n = e[o];
              if ((0, s.Tn)(n)) t[o] = nt(o, n, r);
              else if (null != n) {
                0;
                const e = tt(n);
                t[o] = () => e
              }
            }
          },
          st = (e, t) => {
            const n = tt(t);
            e.slots.default = () => n
          },
          ot = (e, t, n) => {
            for (const r in t)(n || "_" !== r) && (e[r] = t[r])
          },
          it = (e, t, n) => {
            const r = e.slots = Xe();
            if (32 & e.vnode.shapeFlag) {
              const e = t._;
              e ? (ot(r, t, n), n && (0, s.yQ)(r, "_", e, !0)) : rt(t, r)
            } else t && st(e, t)
          },
          at = (e, t, n) => {
            const {
              vnode: r,
              slots: o
            } = e;
            let i = !0,
              a = s.MZ;
            if (32 & r.shapeFlag) {
              const e = t._;
              e ? n && 1 === e ? i = !1 : ot(o, t, n) : (i = !t.$stable, rt(t, o)), a = t
            } else t && (st(e, t), a = {
              default: 1
            });
            if (i)
              for (const s in o) et(s) || null != a[s] || delete o[s]
          };

        function lt() {}
        const ct = Bt;

        function ut(e) {
          return ft(e)
        }

        function ft(e, t) {
          lt();
          const n = (0, s.We)();
          n.__VUE__ = !0;
          const {
            insert: o,
            remove: i,
            patchProp: a,
            createElement: l,
            createText: c,
            createComment: u,
            setText: f,
            setElementText: d,
            parentNode: h,
            nextSibling: p,
            setScopeId: m = s.tE,
            insertStaticContent: g
          } = e, v = (e, t, n, r = null, s = null, o = null, i = void 0, a = null, l = !!t.dynamicChildren) => {
            if (e === t) return;
            e && !Jt(e, t) && (r = Y(e), z(e, s, o, !0), e = null), -2 === t.patchFlag && (l = !1, t.dynamicChildren = null);
            const {
              type: c,
              ref: u,
              shapeFlag: f
            } = t;
            switch (c) {
              case Dt:
                b(e, t, n, r);
                break;
              case It:
                S(e, t, n, r);
                break;
              case Ut:
                null == e && x(t, n, r, i);
                break;
              case jt:
                M(e, t, n, r, s, o, i, a, l);
                break;
              default:
                1 & f ? T(e, t, n, r, s, o, i, a, l) : 6 & f ? B(e, t, n, r, s, o, i, a, l) : (64 & f || 128 & f) && c.process(e, t, n, r, s, o, i, a, l, ne)
            }
            null != u && s && q(u, e && e.ref, o, t || e, !t)
          }, b = (e, t, n, r) => {
            if (null == e) o(t.el = c(t.children), n, r);
            else {
              const n = t.el = e.el;
              t.children !== e.children && f(n, t.children)
            }
          }, S = (e, t, n, r) => {
            null == e ? o(t.el = u(t.children || ""), n, r) : t.el = e.el
          }, x = (e, t, n, r) => {
            [e.el, e.anchor] = g(e.children, t, n, r, e.el, e.anchor)
          }, _ = ({
            el: e,
            anchor: t
          }, n, r) => {
            let s;
            while (e && e !== t) s = p(e), o(e, n, r), e = s;
            o(t, n, r)
          }, E = ({
            el: e,
            anchor: t
          }) => {
            let n;
            while (e && e !== t) n = p(e), i(e), e = n;
            i(t)
          }, T = (e, t, n, r, s, o, i, a, l) => {
            "svg" === t.type ? i = "svg" : "math" === t.type && (i = "mathml"), null == e ? C(t, n, r, s, o, i, a, l) : P(e, t, s, o, i, a, l)
          }, C = (e, t, n, r, i, c, u, f) => {
            let h, p;
            const {
              props: m,
              shapeFlag: g,
              transition: v,
              dirs: y
            } = e;
            if (h = e.el = l(e.type, c, m && m.is, m), 8 & g ? d(h, e.children) : 16 & g && R(e.children, h, null, r, i, dt(e, c), u, f), y && F(e, null, r, "created"), O(h, e, e.scopeId, u, r), m) {
              for (const e in m) "value" === e || (0, s.SU)(e) || a(h, e, null, m[e], c, r);
              "value" in m && a(h, "value", null, m.value, c), (p = m.onVnodeBeforeMount) && hn(p, r, e)
            }
            y && F(e, null, r, "beforeMount");
            const b = pt(i, v);
            b && v.beforeEnter(h), o(h, t, n), ((p = m && m.onVnodeMounted) || b || y) && ct((() => {
              p && hn(p, r, e), b && v.enter(h), y && F(e, null, r, "mounted")
            }), i)
          }, O = (e, t, n, r, s) => {
            if (n && m(e, n), r)
              for (let o = 0; o < r.length; o++) m(e, r[o]);
            if (s) {
              let n = s.subTree;
              if (t === n || Mt(n.type) && (n.ssContent === t || n.ssFallback === t)) {
                const t = s.vnode;
                O(e, t, t.scopeId, t.slotScopeIds, s.parent)
              }
            }
          }, R = (e, t, n, r, s, o, i, a, l = 0) => {
            for (let c = l; c < e.length; c++) {
              const l = e[c] = a ? un(e[c]) : cn(e[c]);
              v(null, l, t, n, r, s, o, i, a)
            }
          }, P = (e, t, n, r, o, i, l) => {
            const c = t.el = e.el;
            let {
              patchFlag: u,
              dynamicChildren: f,
              dirs: h
            } = t;
            u |= 16 & e.patchFlag;
            const p = e.props || s.MZ,
              m = t.props || s.MZ;
            let g;
            if (n && ht(n, !1), (g = m.onVnodeBeforeUpdate) && hn(g, n, t, e), h && F(t, e, n, "beforeUpdate"), n && ht(n, !0), (p.innerHTML && null == m.innerHTML || p.textContent && null == m.textContent) && d(c, ""), f ? N(e.dynamicChildren, f, c, n, r, dt(t, o), i) : l || V(e, t, c, null, n, r, dt(t, o), i, !1), u > 0) {
              if (16 & u) A(c, p, m, n, o);
              else if (2 & u && p.class !== m.class && a(c, "class", null, m.class, o), 4 & u && a(c, "style", p.style, m.style, o), 8 & u) {
                const e = t.dynamicProps;
                for (let t = 0; t < e.length; t++) {
                  const r = e[t],
                    s = p[r],
                    i = m[r];
                  i === s && "value" !== r || a(c, r, s, i, o, n)
                }
              }
              1 & u && e.children !== t.children && d(c, t.children)
            } else l || null != f || A(c, p, m, n, o);
            ((g = m.onVnodeUpdated) || h) && ct((() => {
              g && hn(g, n, t, e), h && F(t, e, n, "updated")
            }), r)
          }, N = (e, t, n, r, s, o, i) => {
            for (let a = 0; a < t.length; a++) {
              const l = e[a],
                c = t[a],
                u = l.el && (l.type === jt || !Jt(l, c) || 70 & l.shapeFlag) ? h(l.el) : n;
              v(l, c, u, null, r, s, o, i, !0)
            }
          }, A = (e, t, n, r, o) => {
            if (t !== n) {
              if (t !== s.MZ)
                for (const i in t)(0, s.SU)(i) || i in n || a(e, i, t[i], null, o, r);
              for (const i in n) {
                if ((0, s.SU)(i)) continue;
                const l = n[i],
                  c = t[i];
                l !== c && "value" !== i && a(e, i, c, l, o, r)
              }
              "value" in n && a(e, "value", t.value, n.value, o)
            }
          }, M = (e, t, n, r, s, i, a, l, u) => {
            const f = t.el = e ? e.el : c(""),
              d = t.anchor = e ? e.anchor : c("");
            let {
              patchFlag: h,
              dynamicChildren: p,
              slotScopeIds: m
            } = t;
            m && (l = l ? l.concat(m) : m), null == e ? (o(f, n, r), o(d, n, r), R(t.children || [], n, d, s, i, a, l, u)) : h > 0 && 64 & h && p && e.dynamicChildren ? (N(e.dynamicChildren, p, n, s, i, a, l), (null != t.key || s && t === s.subTree) && mt(e, t, !0)) : V(e, t, n, d, s, i, a, l, u)
          }, B = (e, t, n, r, s, o, i, a, l) => {
            t.slotScopeIds = a, null == e ? 512 & t.shapeFlag ? s.ctx.activate(t, n, r, i, l) : j(t, n, r, s, o, i, l) : D(e, t, l)
          }, j = (e, t, n, r, s, o, i) => {
            const a = e.component = gn(e, r, s);
            if (K(e) && (a.ctx.renderer = ne), Cn(a, !1, i), a.asyncDep) {
              if (s && s.registerDep(a, I, i), !e.el) {
                const e = a.subTree = tn(It);
                S(null, e, t, n)
              }
            } else I(a, e, t, n, s, o, i)
          }, D = (e, t, n) => {
            const r = t.component = e.component;
            if (Pt(e, t, n)) {
              if (r.asyncDep && !r.asyncResolved) return void U(r, t, n);
              r.next = t, r.update()
            } else t.el = e.el, r.vnode = t
          }, I = (e, t, n, o, i, a, l) => {
            const c = () => {
              if (e.isMounted) {
                let {
                  next: t,
                  bu: n,
                  u: r,
                  parent: o,
                  vnode: u
                } = e; {
                  const n = vt(e);
                  if (n) return t && (t.el = u.el, U(e, t, l)), void n.asyncDep.then((() => {
                    e.isUnmounted || c()
                  }))
                }
                let f, d = t;
                0, ht(e, !1), t ? (t.el = u.el, U(e, t, l)) : t = u, n && (0, s.DY)(n), (f = t.props && t.props.onVnodeBeforeUpdate) && hn(f, o, t, u), ht(e, !0);
                const p = Rt(e);
                0;
                const m = e.subTree;
                e.subTree = p, v(m, p, h(m.el), Y(m), e, i, a), t.el = p.el, null === d && At(e, p.el), r && ct(r, i), (f = t.props && t.props.onVnodeUpdated) && ct((() => hn(f, o, t, u)), i)
              } else {
                let r;
                const {
                  el: l,
                  props: c
                } = t, {
                  bm: u,
                  m: f,
                  parent: d,
                  root: h,
                  type: p
                } = e, m = X(t);
                if (ht(e, !1), u && (0, s.DY)(u), !m && (r = c && c.onVnodeBeforeMount) && hn(r, d, t), ht(e, !0), l && se) {
                  const t = () => {
                    e.subTree = Rt(e), se(l, e.subTree, e, i, null)
                  };
                  m && p.__asyncHydrate ? p.__asyncHydrate(l, e, t) : t()
                } else {
                  h.ce && h.ce._injectChildStyle(p);
                  const r = e.subTree = Rt(e);
                  0, v(null, r, n, o, e, i, a), t.el = r.el
                }
                if (f && ct(f, i), !m && (r = c && c.onVnodeMounted)) {
                  const e = t;
                  ct((() => hn(r, d, e)), i)
                }(256 & t.shapeFlag || d && X(d.vnode) && 256 & d.vnode.shapeFlag) && e.a && ct(e.a, i), e.isMounted = !0, t = n = o = null
              }
            };
            e.scope.on();
            const u = e.effect = new r.X2(c);
            e.scope.off();
            const f = e.update = u.run.bind(u),
              d = e.job = u.runIfDirty.bind(u);
            d.i = e, d.id = e.uid, u.scheduler = () => y(d), ht(e, !0), f()
          }, U = (e, t, n) => {
            t.component = e;
            const s = e.vnode.props;
            e.vnode = t, e.next = null, ze(e, t.props, s, n), at(e, t.children, n), (0, r.C4)(), w(e), (0, r.bl)()
          }, V = (e, t, n, r, s, o, i, a, l = !1) => {
            const c = e && e.children,
              u = e ? e.shapeFlag : 0,
              f = t.children,
              {
                patchFlag: h,
                shapeFlag: p
              } = t;
            if (h > 0) {
              if (128 & h) return void W(c, f, n, r, s, o, i, a, l);
              if (256 & h) return void $(c, f, n, r, s, o, i, a, l)
            }
            8 & p ? (16 & u && Z(c, s, o), f !== c && d(n, f)) : 16 & u ? 16 & p ? W(c, f, n, r, s, o, i, a, l) : Z(c, s, o, !0) : (8 & u && d(n, ""), 16 & p && R(f, n, r, s, o, i, a, l))
          }, $ = (e, t, n, r, o, i, a, l, c) => {
            e = e || s.Oj, t = t || s.Oj;
            const u = e.length,
              f = t.length,
              d = Math.min(u, f);
            let h;
            for (h = 0; h < d; h++) {
              const r = t[h] = c ? un(t[h]) : cn(t[h]);
              v(e[h], r, n, null, o, i, a, l, c)
            }
            u > f ? Z(e, o, i, !0, !1, d) : R(t, n, r, o, i, a, l, c, d)
          }, W = (e, t, n, r, o, i, a, l, c) => {
            let u = 0;
            const f = t.length;
            let d = e.length - 1,
              h = f - 1;
            while (u <= d && u <= h) {
              const r = e[u],
                s = t[u] = c ? un(t[u]) : cn(t[u]);
              if (!Jt(r, s)) break;
              v(r, s, n, null, o, i, a, l, c), u++
            }
            while (u <= d && u <= h) {
              const r = e[d],
                s = t[h] = c ? un(t[h]) : cn(t[h]);
              if (!Jt(r, s)) break;
              v(r, s, n, null, o, i, a, l, c), d--, h--
            }
            if (u > d) {
              if (u <= h) {
                const e = h + 1,
                  s = e < f ? t[e].el : r;
                while (u <= h) v(null, t[u] = c ? un(t[u]) : cn(t[u]), n, s, o, i, a, l, c), u++
              }
            } else if (u > h)
              while (u <= d) z(e[u], o, i, !0), u++;
            else {
              const p = u,
                m = u,
                g = new Map;
              for (u = m; u <= h; u++) {
                const e = t[u] = c ? un(t[u]) : cn(t[u]);
                null != e.key && g.set(e.key, u)
              }
              let y, b = 0;
              const S = h - m + 1;
              let w = !1,
                k = 0;
              const x = new Array(S);
              for (u = 0; u < S; u++) x[u] = 0;
              for (u = p; u <= d; u++) {
                const r = e[u];
                if (b >= S) {
                  z(r, o, i, !0);
                  continue
                }
                let s;
                if (null != r.key) s = g.get(r.key);
                else
                  for (y = m; y <= h; y++)
                    if (0 === x[y - m] && Jt(r, t[y])) {
                      s = y;
                      break
                    } void 0 === s ? z(r, o, i, !0) : (x[s - m] = u + 1, s >= k ? k = s : w = !0, v(r, t[s], n, null, o, i, a, l, c), b++)
              }
              const _ = w ? gt(x) : s.Oj;
              for (y = _.length - 1, u = S - 1; u >= 0; u--) {
                const e = m + u,
                  s = t[e],
                  d = e + 1 < f ? t[e + 1].el : r;
                0 === x[u] ? v(null, s, n, d, o, i, a, l, c) : w && (y < 0 || u !== _[y] ? H(s, n, d, 2) : y--)
              }
            }
          }, H = (e, t, n, r, s = null) => {
            const {
              el: i,
              type: a,
              transition: l,
              children: c,
              shapeFlag: u
            } = e;
            if (6 & u) return void H(e.component.subTree, t, n, r);
            if (128 & u) return void e.suspense.move(t, n, r);
            if (64 & u) return void a.move(e, t, n, ne);
            if (a === jt) {
              o(i, t, n);
              for (let e = 0; e < c.length; e++) H(c[e], t, n, r);
              return void o(e.anchor, t, n)
            }
            if (a === Ut) return void _(e, t, n);
            const f = 2 !== r && 1 & u && l;
            if (f)
              if (0 === r) l.beforeEnter(i), o(i, t, n), ct((() => l.enter(i)), s);
              else {
                const {
                  leave: e,
                  delayLeave: r,
                  afterLeave: s
                } = l, a = () => o(i, t, n), c = () => {
                  e(i, (() => {
                    a(), s && s()
                  }))
                };
                r ? r(i, a, c) : c()
              }
            else o(i, t, n)
          }, z = (e, t, n, r = !1, s = !1) => {
            const {
              type: o,
              props: i,
              ref: a,
              children: l,
              dynamicChildren: c,
              shapeFlag: u,
              patchFlag: f,
              dirs: d,
              cacheIndex: h
            } = e;
            if (-2 === f && (s = !1), null != a && q(a, null, n, e, !0), null != h && (t.renderCache[h] = void 0), 256 & u) return void t.ctx.deactivate(e);
            const p = 1 & u && d,
              m = !X(e);
            let g;
            if (m && (g = i && i.onVnodeBeforeUnmount) && hn(g, t, e), 6 & u) J(e.component, n, r);
            else {
              if (128 & u) return void e.suspense.unmount(n, r);
              p && F(e, null, t, "beforeUnmount"), 64 & u ? e.type.remove(e, t, n, ne, r) : c && !c.hasOnce && (o !== jt || f > 0 && 64 & f) ? Z(c, t, n, !1, !0) : (o === jt && 384 & f || !s && 16 & u) && Z(l, t, n), r && G(e)
            }(m && (g = i && i.onVnodeUnmounted) || p) && ct((() => {
              g && hn(g, t, e), p && F(e, null, t, "unmounted")
            }), n)
          }, G = e => {
            const {
              type: t,
              el: n,
              anchor: r,
              transition: s
            } = e;
            if (t === jt) return void Q(n, r);
            if (t === Ut) return void E(e);
            const o = () => {
              i(n), s && !s.persisted && s.afterLeave && s.afterLeave()
            };
            if (1 & e.shapeFlag && s && !s.persisted) {
              const {
                leave: t,
                delayLeave: r
              } = s, i = () => t(n, o);
              r ? r(e.el, o, i) : i()
            } else o()
          }, Q = (e, t) => {
            let n;
            while (e !== t) n = p(e), i(e), e = n;
            i(t)
          }, J = (e, t, n) => {
            const {
              bum: r,
              scope: o,
              job: i,
              subTree: a,
              um: l,
              m: c,
              a: u
            } = e;
            yt(c), yt(u), r && (0, s.DY)(r), o.stop(), i && (i.flags |= 8, z(a, e, t, n)), l && ct(l, t), ct((() => {
              e.isUnmounted = !0
            }), t), t && t.pendingBranch && !t.isUnmounted && e.asyncDep && !e.asyncResolved && e.suspenseId === t.pendingId && (t.deps--, 0 === t.deps && t.resolve())
          }, Z = (e, t, n, r = !1, s = !1, o = 0) => {
            for (let i = o; i < e.length; i++) z(e[i], t, n, r, s)
          }, Y = e => {
            if (6 & e.shapeFlag) return Y(e.component.subTree);
            if (128 & e.shapeFlag) return e.suspense.next();
            const t = p(e.anchor || e.el),
              n = t && t[L];
            return n ? p(n) : t
          };
          let ee = !1;
          const te = (e, t, n) => {
              null == e ? t._vnode && z(t._vnode, null, null, !0) : v(t._vnode || null, e, t, null, null, null, n), t._vnode = e, ee || (ee = !0, w(), k(), ee = !1)
            },
            ne = {
              p: v,
              um: z,
              m: H,
              r: G,
              mt: j,
              mc: R,
              pc: V,
              pbc: N,
              n: Y,
              o: e
            };
          let re, se;
          return t && ([re, se] = t(ne)), {
            render: te,
            hydrate: re,
            createApp: Ue(te, re)
          }
        }

        function dt({
          type: e,
          props: t
        }, n) {
          return "svg" === n && "foreignObject" === e || "mathml" === n && "annotation-xml" === e && t && t.encoding && t.encoding.includes("html") ? void 0 : n
        }

        function ht({
          effect: e,
          job: t
        }, n) {
          n ? (e.flags |= 32, t.flags |= 4) : (e.flags &= -33, t.flags &= -5)
        }

        function pt(e, t) {
          return (!e || e && !e.pendingBranch) && t && !t.persisted
        }

        function mt(e, t, n = !1) {
          const r = e.children,
            o = t.children;
          if ((0, s.cy)(r) && (0, s.cy)(o))
            for (let s = 0; s < r.length; s++) {
              const e = r[s];
              let t = o[s];
              1 & t.shapeFlag && !t.dynamicChildren && ((t.patchFlag <= 0 || 32 === t.patchFlag) && (t = o[s] = un(o[s]), t.el = e.el), n || -2 === t.patchFlag || mt(e, t)), t.type === Dt && (t.el = e.el)
            }
        }

        function gt(e) {
          const t = e.slice(),
            n = [0];
          let r, s, o, i, a;
          const l = e.length;
          for (r = 0; r < l; r++) {
            const l = e[r];
            if (0 !== l) {
              if (s = n[n.length - 1], e[s] < l) {
                t[r] = s, n.push(r);
                continue
              }
              o = 0, i = n.length - 1;
              while (o < i) a = o + i >> 1, e[n[a]] < l ? o = a + 1 : i = a;
              l < e[n[o]] && (o > 0 && (t[r] = n[o - 1]), n[o] = r)
            }
          }
          o = n.length, i = n[o - 1];
          while (o-- > 0) n[o] = i, i = t[i];
          return n
        }

        function vt(e) {
          const t = e.subTree.component;
          if (t) return t.asyncDep && !t.asyncResolved ? t : vt(t)
        }

        function yt(e) {
          if (e)
            for (let t = 0; t < e.length; t++) e[t].flags |= 8
        }
        const bt = Symbol.for("v-scx"),
          St = () => {
            {
              const e = We(bt);
              return e
            }
          };

        function wt(e, t, n) {
          return kt(e, t, n)
        }

        function kt(e, t, n = s.MZ) {
          const {
            immediate: o,
            deep: a,
            flush: l,
            once: c
          } = n;
          const u = (0, s.X$)({}, n);
          const f = t && o || !t && "post" !== l;
          let d;
          if (Tn)
            if ("sync" === l) {
              const e = St();
              d = e.__watcherHandles || (e.__watcherHandles = [])
            } else if (!f) {
            const e = () => {};
            return e.stop = s.tE, e.resume = s.tE, e.pause = s.tE, e
          }
          const h = vn;
          u.call = (e, t, n) => i(e, h, t, n);
          let p = !1;
          "post" === l ? u.scheduler = e => {
            ct(e, h && h.suspense)
          } : "sync" !== l && (p = !0, u.scheduler = (e, t) => {
            t ? e() : y(e)
          }), u.augmentJob = e => {
            t && (e.flags |= 4), p && (e.flags |= 2, h && (e.id = h.uid, e.i = h))
          };
          const m = (0, r.wB)(e, t, u);
          return Tn && (d ? d.push(m) : f && m()), m
        }

        function xt(e, t, n) {
          const r = this.proxy,
            o = (0, s.Kg)(e) ? e.includes(".") ? _t(r, e) : () => r[e] : e.bind(r, r);
          let i;
          (0, s.Tn)(t) ? i = t: (i = t.handler, n = t);
          const a = wn(this),
            l = kt(o, i.bind(r), n);
          return a(), l
        }

        function _t(e, t) {
          const n = t.split(".");
          return () => {
            let t = e;
            for (let e = 0; e < n.length && t; e++) t = t[n[e]];
            return t
          }
        }
        const Et = (e, t) => "modelValue" === t || "model-value" === t ? e.modelModifiers : e[`${t}Modifiers`] || e[`${(0,s.PT)(t)}Modifiers`] || e[`${(0,s.Tg)(t)}Modifiers`];

        function Tt(e, t, ...n) {
          if (e.isUnmounted) return;
          const r = e.vnode.props || s.MZ;
          let o = n;
          const a = t.startsWith("update:"),
            l = a && Et(r, t.slice(7));
          let c;
          l && (l.trim && (o = n.map((e => (0, s.Kg)(e) ? e.trim() : e))), l.number && (o = n.map(s.bB)));
          let u = r[c = (0, s.rU)(t)] || r[c = (0, s.rU)((0, s.PT)(t))];
          !u && a && (u = r[c = (0, s.rU)((0, s.Tg)(t))]), u && i(u, e, 6, o);
          const f = r[c + "Once"];
          if (f) {
            if (e.emitted) {
              if (e.emitted[c]) return
            } else e.emitted = {};
            e.emitted[c] = !0, i(f, e, 6, o)
          }
        }

        function Ct(e, t, n = !1) {
          const r = t.emitsCache,
            o = r.get(e);
          if (void 0 !== o) return o;
          const i = e.emits;
          let a = {},
            l = !1;
          if (!(0, s.Tn)(e)) {
            const r = e => {
              const n = Ct(e, t, !0);
              n && (l = !0, (0, s.X$)(a, n))
            };
            !n && t.mixins.length && t.mixins.forEach(r), e.extends && r(e.extends), e.mixins && e.mixins.forEach(r)
          }
          return i || l ? ((0, s.cy)(i) ? i.forEach((e => a[e] = null)) : (0, s.X$)(a, i), (0, s.Gv)(e) && r.set(e, a), a) : ((0, s.Gv)(e) && r.set(e, null), null)
        }

        function Ot(e, t) {
          return !(!e || !(0, s.Mp)(t)) && (t = t.slice(2).replace(/Once$/, ""), (0, s.$3)(e, t[0].toLowerCase() + t.slice(1)) || (0, s.$3)(e, (0, s.Tg)(t)) || (0, s.$3)(e, t))
        }

        function Rt(e) {
          const {
            type: t,
            vnode: n,
            proxy: r,
            withProxy: o,
            propsOptions: [i],
            slots: l,
            attrs: c,
            emit: u,
            render: f,
            renderCache: d,
            props: h,
            data: p,
            setupState: m,
            ctx: g,
            inheritAttrs: v
          } = e, y = C(e);
          let b, S;
          try {
            if (4 & n.shapeFlag) {
              const e = o || r,
                t = e;
              b = cn(f.call(t, e, d, h, m, p, g)), S = c
            } else {
              const e = t;
              0, b = cn(e.length > 1 ? e(h, {
                attrs: c,
                slots: l,
                emit: u
              }) : e(h, null)), S = t.props ? c : Ft(c)
            }
          } catch (k) {
            Vt.length = 0, a(k, e, 1), b = tn(It)
          }
          let w = b;
          if (S && !1 !== v) {
            const e = Object.keys(S),
              {
                shapeFlag: t
              } = w;
            e.length && 7 & t && (i && e.some(s.CP) && (S = Lt(S, i)), w = sn(w, S, !1, !0))
          }
          return n.dirs && (w = sn(w, null, !1, !0), w.dirs = w.dirs ? w.dirs.concat(n.dirs) : n.dirs), n.transition && U(w, n.transition), b = w, C(y), b
        }
        const Ft = e => {
            let t;
            for (const n in e)("class" === n || "style" === n || (0, s.Mp)(n)) && ((t || (t = {}))[n] = e[n]);
            return t
          },
          Lt = (e, t) => {
            const n = {};
            for (const r in e)(0, s.CP)(r) && r.slice(9) in t || (n[r] = e[r]);
            return n
          };

        function Pt(e, t, n) {
          const {
            props: r,
            children: s,
            component: o
          } = e, {
            props: i,
            children: a,
            patchFlag: l
          } = t, c = o.emitsOptions;
          if (t.dirs || t.transition) return !0;
          if (!(n && l >= 0)) return !(!s && !a || a && a.$stable) || r !== i && (r ? !i || Nt(r, i, c) : !!i);
          if (1024 & l) return !0;
          if (16 & l) return r ? Nt(r, i, c) : !!i;
          if (8 & l) {
            const e = t.dynamicProps;
            for (let t = 0; t < e.length; t++) {
              const n = e[t];
              if (i[n] !== r[n] && !Ot(c, n)) return !0
            }
          }
          return !1
        }

        function Nt(e, t, n) {
          const r = Object.keys(t);
          if (r.length !== Object.keys(e).length) return !0;
          for (let s = 0; s < r.length; s++) {
            const o = r[s];
            if (t[o] !== e[o] && !Ot(n, o)) return !0
          }
          return !1
        }

        function At({
          vnode: e,
          parent: t
        }, n) {
          while (t) {
            const r = t.subTree;
            if (r.suspense && r.suspense.activeBranch === e && (r.el = e.el), r !== e) break;
            (e = t.vnode).el = n, t = t.parent
          }
        }
        const Mt = e => e.__isSuspense;

        function Bt(e, t) {
          t && t.pendingBranch ? (0, s.cy)(e) ? t.effects.push(...e) : t.effects.push(e) : S(e)
        }
        const jt = Symbol.for("v-fgt"),
          Dt = Symbol.for("v-txt"),
          It = Symbol.for("v-cmt"),
          Ut = Symbol.for("v-stc"),
          Vt = [];
        let $t = null;

        function Wt(e = !1) {
          Vt.push($t = e ? null : [])
        }

        function qt() {
          Vt.pop(), $t = Vt[Vt.length - 1] || null
        }
        let Xt = 1;

        function Kt(e) {
          Xt += e, e < 0 && $t && ($t.hasOnce = !0)
        }

        function Ht(e) {
          return e.dynamicChildren = Xt > 0 ? $t || s.Oj : null, qt(), Xt > 0 && $t && $t.push(e), e
        }

        function zt(e, t, n, r, s, o) {
          return Ht(en(e, t, n, r, s, o, !0))
        }

        function Gt(e, t, n, r, s) {
          return Ht(tn(e, t, n, r, s, !0))
        }

        function Qt(e) {
          return !!e && !0 === e.__v_isVNode
        }

        function Jt(e, t) {
          return e.type === t.type && e.key === t.key
        }
        const Zt = ({
            key: e
          }) => null != e ? e : null,
          Yt = ({
            ref: e,
            ref_key: t,
            ref_for: n
          }) => ("number" === typeof e && (e = "" + e), null != e ? (0, s.Kg)(e) || (0, r.i9)(e) || (0, s.Tn)(e) ? {
            i: E,
            r: e,
            k: t,
            f: !!n
          } : e : null);

        function en(e, t = null, n = null, r = 0, o = null, i = (e === jt ? 0 : 1), a = !1, l = !1) {
          const c = {
            __v_isVNode: !0,
            __v_skip: !0,
            type: e,
            props: t,
            key: t && Zt(t),
            ref: t && Yt(t),
            scopeId: T,
            slotScopeIds: null,
            children: n,
            component: null,
            suspense: null,
            ssContent: null,
            ssFallback: null,
            dirs: null,
            transition: null,
            el: null,
            anchor: null,
            target: null,
            targetStart: null,
            targetAnchor: null,
            staticCount: 0,
            shapeFlag: i,
            patchFlag: r,
            dynamicProps: o,
            dynamicChildren: null,
            appContext: null,
            ctx: E
          };
          return l ? (fn(c, n), 128 & i && e.normalize(c)) : n && (c.shapeFlag |= (0, s.Kg)(n) ? 8 : 16), Xt > 0 && !a && $t && (c.patchFlag > 0 || 6 & i) && 32 !== c.patchFlag && $t.push(c), c
        }
        const tn = nn;

        function nn(e, t = null, n = null, o = 0, i = null, a = !1) {
          if (e && e !== pe || (e = It), Qt(e)) {
            const r = sn(e, t, !0);
            return n && fn(r, n), Xt > 0 && !a && $t && (6 & r.shapeFlag ? $t[$t.indexOf(e)] = r : $t.push(r)), r.patchFlag = -2, r
          }
          if (Mn(e) && (e = e.__vccOpts), t) {
            t = rn(t);
            let {
              class: e,
              style: n
            } = t;
            e && !(0, s.Kg)(e) && (t.class = (0, s.C4)(e)), (0, s.Gv)(n) && ((0, r.ju)(n) && !(0, s.cy)(n) && (n = (0, s.X$)({}, n)), t.style = (0, s.Tr)(n))
          }
          const l = (0, s.Kg)(e) ? 1 : Mt(e) ? 128 : P(e) ? 64 : (0, s.Gv)(e) ? 4 : (0, s.Tn)(e) ? 2 : 0;
          return en(e, t, n, o, i, l, a, !0)
        }

        function rn(e) {
          return e ? (0, r.ju)(e) || Ke(e) ? (0, s.X$)({}, e) : e : null
        }

        function sn(e, t, n = !1, r = !1) {
          const {
            props: o,
            ref: i,
            patchFlag: a,
            children: l,
            transition: c
          } = e, u = t ? dn(o || {}, t) : o, f = {
            __v_isVNode: !0,
            __v_skip: !0,
            type: e.type,
            props: u,
            key: u && Zt(u),
            ref: t && t.ref ? n && i ? (0, s.cy)(i) ? i.concat(Yt(t)) : [i, Yt(t)] : Yt(t) : i,
            scopeId: e.scopeId,
            slotScopeIds: e.slotScopeIds,
            children: l,
            target: e.target,
            targetStart: e.targetStart,
            targetAnchor: e.targetAnchor,
            staticCount: e.staticCount,
            shapeFlag: e.shapeFlag,
            patchFlag: t && e.type !== jt ? -1 === a ? 16 : 16 | a : a,
            dynamicProps: e.dynamicProps,
            dynamicChildren: e.dynamicChildren,
            appContext: e.appContext,
            dirs: e.dirs,
            transition: c,
            component: e.component,
            suspense: e.suspense,
            ssContent: e.ssContent && sn(e.ssContent),
            ssFallback: e.ssFallback && sn(e.ssFallback),
            el: e.el,
            anchor: e.anchor,
            ctx: e.ctx,
            ce: e.ce
          };
          return c && r && U(f, c.clone(f)), f
        }

        function on(e = " ", t = 0) {
          return tn(Dt, null, e, t)
        }

        function an(e, t) {
          const n = tn(Ut, null, e);
          return n.staticCount = t, n
        }

        function ln(e = "", t = !1) {
          return t ? (Wt(), Gt(It, null, e)) : tn(It, null, e)
        }

        function cn(e) {
          return null == e || "boolean" === typeof e ? tn(It) : (0, s.cy)(e) ? tn(jt, null, e.slice()) : Qt(e) ? un(e) : tn(Dt, null, String(e))
        }

        function un(e) {
          return null === e.el && -1 !== e.patchFlag || e.memo ? e : sn(e)
        }

        function fn(e, t) {
          let n = 0;
          const {
            shapeFlag: r
          } = e;
          if (null == t) t = null;
          else if ((0, s.cy)(t)) n = 16;
          else if ("object" === typeof t) {
            if (65 & r) {
              const n = t.default;
              return void(n && (n._c && (n._d = !1), fn(e, n()), n._c && (n._d = !0)))
            } {
              n = 32;
              const r = t._;
              r || Ke(t) ? 3 === r && E && (1 === E.slots._ ? t._ = 1 : (t._ = 2, e.patchFlag |= 1024)) : t._ctx = E
            }
          } else(0, s.Tn)(t) ? (t = {
            default: t,
            _ctx: E
          }, n = 32) : (t = String(t), 64 & r ? (n = 16, t = [on(t)]) : n = 8);
          e.children = t, e.shapeFlag |= n
        }

        function dn(...e) {
          const t = {};
          for (let n = 0; n < e.length; n++) {
            const r = e[n];
            for (const e in r)
              if ("class" === e) t.class !== r.class && (t.class = (0, s.C4)([t.class, r.class]));
              else if ("style" === e) t.style = (0, s.Tr)([t.style, r.style]);
            else if ((0, s.Mp)(e)) {
              const n = t[e],
                o = r[e];
              !o || n === o || (0, s.cy)(n) && n.includes(o) || (t[e] = n ? [].concat(n, o) : o)
            } else "" !== e && (t[e] = r[e])
          }
          return t
        }

        function hn(e, t, n, r = null) {
          i(e, t, 7, [n, r])
        }
        const pn = De();
        let mn = 0;

        function gn(e, t, n) {
          const o = e.type,
            i = (t ? t.appContext : e.appContext) || pn,
            a = {
              uid: mn++,
              vnode: e,
              type: o,
              parent: t,
              appContext: i,
              root: null,
              next: null,
              subTree: null,
              effect: null,
              update: null,
              job: null,
              scope: new r.yC(!0),
              render: null,
              proxy: null,
              exposed: null,
              exposeProxy: null,
              withProxy: null,
              provides: t ? t.provides : Object.create(i.provides),
              ids: t ? t.ids : ["", 0, 0],
              accessCache: null,
              renderCache: [],
              components: null,
              directives: null,
              propsOptions: Ze(o, i),
              emitsOptions: Ct(o, i),
              emit: null,
              emitted: null,
              propsDefaults: s.MZ,
              inheritAttrs: o.inheritAttrs,
              ctx: s.MZ,
              data: s.MZ,
              props: s.MZ,
              attrs: s.MZ,
              slots: s.MZ,
              refs: s.MZ,
              setupState: s.MZ,
              setupContext: null,
              suspense: n,
              suspenseId: n ? n.pendingId : 0,
              asyncDep: null,
              asyncResolved: !1,
              isMounted: !1,
              isUnmounted: !1,
              isDeactivated: !1,
              bc: null,
              c: null,
              bm: null,
              m: null,
              bu: null,
              u: null,
              um: null,
              bum: null,
              da: null,
              a: null,
              rtg: null,
              rtc: null,
              ec: null,
              sp: null
            };
          return a.ctx = {
            _: a
          }, a.root = t ? t.root : a, a.emit = Tt.bind(null, a), e.ce && e.ce(a), a
        }
        let vn = null;
        const yn = () => vn || E;
        let bn, Sn; {
          const e = (0, s.We)(),
            t = (t, n) => {
              let r;
              return (r = e[t]) || (r = e[t] = []), r.push(n), e => {
                r.length > 1 ? r.forEach((t => t(e))) : r[0](e)
              }
            };
          bn = t("__VUE_INSTANCE_SETTERS__", (e => vn = e)), Sn = t("__VUE_SSR_SETTERS__", (e => Tn = e))
        }
        const wn = e => {
            const t = vn;
            return bn(e), e.scope.on(), () => {
              e.scope.off(), bn(t)
            }
          },
          kn = () => {
            vn && vn.scope.off(), bn(null)
          };

        function xn(e) {
          return 4 & e.vnode.shapeFlag
        }
        let _n, En, Tn = !1;

        function Cn(e, t = !1, n = !1) {
          t && Sn(t);
          const {
            props: r,
            children: s
          } = e.vnode, o = xn(e);
          He(e, r, o, t), it(e, s, n);
          const i = o ? On(e, t) : void 0;
          return t && Sn(!1), i
        }

        function On(e, t) {
          const n = e.type;
          e.accessCache = Object.create(null), e.proxy = new Proxy(e.ctx, we);
          const {
            setup: i
          } = n;
          if (i) {
            (0, r.C4)();
            const n = e.setupContext = i.length > 1 ? Pn(e) : null,
              l = wn(e),
              c = o(i, e, 0, [e.props, n]),
              u = (0, s.yL)(c);
            if ((0, r.bl)(), l(), !u && !e.sp || X(e) || W(e), u) {
              if (c.then(kn, kn), t) return c.then((n => {
                Rn(e, n, t)
              })).catch((t => {
                a(t, e, 0)
              }));
              e.asyncDep = c
            } else Rn(e, c, t)
          } else Fn(e, t)
        }

        function Rn(e, t, n) {
          (0, s.Tn)(t) ? e.type.__ssrInlineRender ? e.ssrRender = t : e.render = t: (0, s.Gv)(t) && (e.setupState = (0, r.Pr)(t)), Fn(e, n)
        }

        function Fn(e, t, n) {
          const o = e.type;
          if (!e.render) {
            if (!t && _n && !o.render) {
              const t = o.template || Oe(e).template;
              if (t) {
                0;
                const {
                  isCustomElement: n,
                  compilerOptions: r
                } = e.appContext.config, {
                  delimiters: i,
                  compilerOptions: a
                } = o, l = (0, s.X$)((0, s.X$)({
                  isCustomElement: n,
                  delimiters: i
                }, r), a);
                o.render = _n(t, l)
              }
            }
            e.render = o.render || s.tE, En && En(e)
          } {
            const t = wn(e);
            (0, r.C4)();
            try {
              _e(e)
            } finally {
              (0, r.bl)(), t()
            }
          }
        }
        const Ln = {
          get(e, t) {
            return (0, r.u4)(e, "get", ""), e[t]
          }
        };

        function Pn(e) {
          const t = t => {
            e.exposed = t || {}
          };
          return {
            attrs: new Proxy(e.attrs, Ln),
            slots: e.slots,
            emit: e.emit,
            expose: t
          }
        }

        function Nn(e) {
          return e.exposed ? e.exposeProxy || (e.exposeProxy = new Proxy((0, r.Pr)((0, r.IG)(e.exposed)), {
            get(t, n) {
              return n in t ? t[n] : n in be ? be[n](e) : void 0
            },
            has(e, t) {
              return t in e || t in be
            }
          })) : e.proxy
        }

        function An(e, t = !0) {
          return (0, s.Tn)(e) ? e.displayName || e.name : e.name || t && e.__name
        }

        function Mn(e) {
          return (0, s.Tn)(e) && "__vccOpts" in e
        }
        const Bn = (e, t) => {
          const n = (0, r.EW)(e, t, Tn);
          return n
        };

        function jn(e, t, n) {
          const r = arguments.length;
          return 2 === r ? (0, s.Gv)(t) && !(0, s.cy)(t) ? Qt(t) ? tn(e, null, [t]) : tn(e, t) : tn(e, null, t) : (r > 3 ? n = Array.prototype.slice.call(arguments, 2) : 3 === r && Qt(n) && (n = [n]), tn(e, t, n))
        }
        const Dn = "3.5.12"
      },
      33: function (e, t, n) {
        "use strict";
        /**
         * @vue/shared v3.5.12
         * (c) 2018-present Yuxi (Evan) You and Vue contributors
         * @license MIT
         **/
        /*! #__NO_SIDE_EFFECTS__ */
        function r(e) {
          const t = Object.create(null);
          for (const n of e.split(",")) t[n] = 1;
          return e => e in t
        }
        n.d(t, {
          $3: function () {
            return h
          },
          $H: function () {
            return j
          },
          BH: function () {
            return X
          },
          BX: function () {
            return ne
          },
          Bm: function () {
            return w
          },
          C4: function () {
            return J
          },
          CE: function () {
            return m
          },
          CP: function () {
            return c
          },
          DY: function () {
            return D
          },
          Gv: function () {
            return k
          },
          J$: function () {
            return Y
          },
          Kg: function () {
            return S
          },
          MZ: function () {
            return s
          },
          Mp: function () {
            return l
          },
          NO: function () {
            return a
          },
          Oj: function () {
            return o
          },
          PT: function () {
            return P
          },
          Qd: function () {
            return C
          },
          Ro: function () {
            return V
          },
          SU: function () {
            return R
          },
          TF: function () {
            return f
          },
          Tg: function () {
            return A
          },
          Tn: function () {
            return b
          },
          Tr: function () {
            return K
          },
          We: function () {
            return W
          },
          X$: function () {
            return u
          },
          Y2: function () {
            return ee
          },
          ZH: function () {
            return M
          },
          Zf: function () {
            return T
          },
          bB: function () {
            return U
          },
          cy: function () {
            return p
          },
          gd: function () {
            return y
          },
          pD: function () {
            return r
          },
          rU: function () {
            return B
          },
          tE: function () {
            return i
          },
          u3: function () {
            return re
          },
          vM: function () {
            return g
          },
          v_: function () {
            return oe
          },
          yI: function () {
            return O
          },
          yL: function () {
            return x
          },
          yQ: function () {
            return I
          }
        });
        const s = {},
          o = [],
          i = () => {},
          a = () => !1,
          l = e => 111 === e.charCodeAt(0) && 110 === e.charCodeAt(1) && (e.charCodeAt(2) > 122 || e.charCodeAt(2) < 97),
          c = e => e.startsWith("onUpdate:"),
          u = Object.assign,
          f = (e, t) => {
            const n = e.indexOf(t);
            n > -1 && e.splice(n, 1)
          },
          d = Object.prototype.hasOwnProperty,
          h = (e, t) => d.call(e, t),
          p = Array.isArray,
          m = e => "[object Map]" === E(e),
          g = e => "[object Set]" === E(e),
          v = e => "[object Date]" === E(e),
          y = e => "[object RegExp]" === E(e),
          b = e => "function" === typeof e,
          S = e => "string" === typeof e,
          w = e => "symbol" === typeof e,
          k = e => null !== e && "object" === typeof e,
          x = e => (k(e) || b(e)) && b(e.then) && b(e.catch),
          _ = Object.prototype.toString,
          E = e => _.call(e),
          T = e => E(e).slice(8, -1),
          C = e => "[object Object]" === E(e),
          O = e => S(e) && "NaN" !== e && "-" !== e[0] && "" + parseInt(e, 10) === e,
          R = r(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),
          F = e => {
            const t = Object.create(null);
            return n => {
              const r = t[n];
              return r || (t[n] = e(n))
            }
          },
          L = /-(\w)/g,
          P = F((e => e.replace(L, ((e, t) => t ? t.toUpperCase() : "")))),
          N = /\B([A-Z])/g,
          A = F((e => e.replace(N, "-$1").toLowerCase())),
          M = F((e => e.charAt(0).toUpperCase() + e.slice(1))),
          B = F((e => {
            const t = e ? `on${M(e)}` : "";
            return t
          })),
          j = (e, t) => !Object.is(e, t),
          D = (e, ...t) => {
            for (let n = 0; n < e.length; n++) e[n](...t)
          },
          I = (e, t, n, r = !1) => {
            Object.defineProperty(e, t, {
              configurable: !0,
              enumerable: !1,
              writable: r,
              value: n
            })
          },
          U = e => {
            const t = parseFloat(e);
            return isNaN(t) ? e : t
          },
          V = e => {
            const t = S(e) ? Number(e) : NaN;
            return isNaN(t) ? e : t
          };
        let $;
        const W = () => $ || ($ = "undefined" !== typeof globalThis ? globalThis : "undefined" !== typeof self ? self : "undefined" !== typeof window ? window : "undefined" !== typeof n.g ? n.g : {});
        const q = "Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt,console,Error,Symbol",
          X = r(q);

        function K(e) {
          if (p(e)) {
            const t = {};
            for (let n = 0; n < e.length; n++) {
              const r = e[n],
                s = S(r) ? Q(r) : K(r);
              if (s)
                for (const e in s) t[e] = s[e]
            }
            return t
          }
          if (S(e) || k(e)) return e
        }
        const H = /;(?![^(]*\))/g,
          z = /:([^]+)/,
          G = /\/\*[^]*?\*\//g;

        function Q(e) {
          const t = {};
          return e.replace(G, "").split(H).forEach((e => {
            if (e) {
              const n = e.split(z);
              n.length > 1 && (t[n[0].trim()] = n[1].trim())
            }
          })), t
        }

        function J(e) {
          let t = "";
          if (S(e)) t = e;
          else if (p(e))
            for (let n = 0; n < e.length; n++) {
              const r = J(e[n]);
              r && (t += r + " ")
            } else if (k(e))
              for (const n in e) e[n] && (t += n + " ");
          return t.trim()
        }
        const Z = "itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",
          Y = r(Z);

        function ee(e) {
          return !!e || "" === e
        }

        function te(e, t) {
          if (e.length !== t.length) return !1;
          let n = !0;
          for (let r = 0; n && r < e.length; r++) n = ne(e[r], t[r]);
          return n
        }

        function ne(e, t) {
          if (e === t) return !0;
          let n = v(e),
            r = v(t);
          if (n || r) return !(!n || !r) && e.getTime() === t.getTime();
          if (n = w(e), r = w(t), n || r) return e === t;
          if (n = p(e), r = p(t), n || r) return !(!n || !r) && te(e, t);
          if (n = k(e), r = k(t), n || r) {
            if (!n || !r) return !1;
            const s = Object.keys(e).length,
              o = Object.keys(t).length;
            if (s !== o) return !1;
            for (const n in e) {
              const r = e.hasOwnProperty(n),
                s = t.hasOwnProperty(n);
              if (r && !s || !r && s || !ne(e[n], t[n])) return !1
            }
          }
          return String(e) === String(t)
        }

        function re(e, t) {
          return e.findIndex((e => ne(e, t)))
        }
        const se = e => !(!e || !0 !== e["__v_isRef"]),
          oe = e => S(e) ? e : null == e ? "" : p(e) || k(e) && (e.toString === _ || !b(e.toString)) ? se(e) ? oe(e.value) : JSON.stringify(e, ie, 2) : String(e),
          ie = (e, t) => se(t) ? ie(e, t.value) : m(t) ? {
            [`Map(${t.size})`]: [...t.entries()].reduce(((e, [t, n], r) => (e[ae(t, r) + " =>"] = n, e)), {})
          } : g(t) ? {
            [`Set(${t.size})`]: [...t.values()].map((e => ae(e)))
          } : w(t) ? ae(t) : !k(t) || p(t) || C(t) ? t : String(t),
          ae = (e, t = "") => {
            var n;
            return w(e) ? `Symbol(${null!=(n=e.description)?n:t})` : e
          }
      },
      8407: function (e, t, n) {
        "use strict";
        n.r(t);
        var r = n(1601),
          s = n.n(r),
          o = n(6314),
          i = n.n(o),
          a = i()(s());
        a.push([e.id, "#open[data-v-3a57803b],#remove[data-v-3a57803b]{margin-right:7px;color:grey}#save[data-v-3a57803b]{color:grey}#backBtn[data-v-3a57803b],#searchBtn[data-v-3a57803b]{width:100%}.loadSetting[data-v-3a57803b]{margin-right:7px}ul[data-v-3a57803b]{list-style-type:none;padding:0;margin:0}.input-group-text[data-v-3a57803b]{font-size:.75rem}.remove[data-v-3a57803b]{color:grey;cursor:pointer}.alert-msg[data-v-3a57803b],.results[data-v-3a57803b]{font-size:12px}", ""]), t["default"] = a
      },
      3560: function (e, t, n) {
        "use strict";
        n.r(t);
        var r = n(1601),
          s = n.n(r),
          o = n(6314),
          i = n.n(o),
          a = i()(s());
        a.push([e.id, ".version[data-v-5128f18e]{font-size:1.3rem}.mode[data-v-5128f18e]{font-size:1.1rem}", ""]), t["default"] = a
      },
      1203: function (e, t, n) {
        "use strict";
        n.r(t);
        var r = n(1601),
          s = n.n(r),
          o = n(6314),
          i = n.n(o),
          a = i()(s());
        a.push([e.id, "button[data-v-d54a9c32]{width:85px}.settings[data-v-d54a9c32]{height:410px}", ""]), t["default"] = a
      },
      8354: function (e, t, n) {
        "use strict";
        n.r(t);
        var r = n(1601),
          s = n.n(r),
          o = n(6314),
          i = n.n(o),
          a = i()(s());
        a.push([e.id, "html{width:400px;height:400px}", ""]), t["default"] = a
      },
      6314: function (e) {
        "use strict";
        e.exports = function (e) {
          var t = [];
          return t.toString = function () {
            return this.map((function (t) {
              var n = "",
                r = "undefined" !== typeof t[5];
              return t[4] && (n += "@supports (".concat(t[4], ") {")), t[2] && (n += "@media ".concat(t[2], " {")), r && (n += "@layer".concat(t[5].length > 0 ? " ".concat(t[5]) : "", " {")), n += e(t), r && (n += "}"), t[2] && (n += "}"), t[4] && (n += "}"), n
            })).join("")
          }, t.i = function (e, n, r, s, o) {
            "string" === typeof e && (e = [
              [null, e, void 0]
            ]);
            var i = {};
            if (r)
              for (var a = 0; a < this.length; a++) {
                var l = this[a][0];
                null != l && (i[l] = !0)
              }
            for (var c = 0; c < e.length; c++) {
              var u = [].concat(e[c]);
              r && i[u[0]] || ("undefined" !== typeof o && ("undefined" === typeof u[5] || (u[1] = "@layer".concat(u[5].length > 0 ? " ".concat(u[5]) : "", " {").concat(u[1], "}")), u[5] = o), n && (u[2] ? (u[1] = "@media ".concat(u[2], " {").concat(u[1], "}"), u[2] = n) : u[2] = n), s && (u[4] ? (u[1] = "@supports (".concat(u[4], ") {").concat(u[1], "}"), u[4] = s) : u[4] = "".concat(s)), t.push(u))
            }
          }, t
        }
      },
      1601: function (e) {
        "use strict";
        e.exports = function (e) {
          return e[1]
        }
      },
      6262: function (e, t) {
        "use strict";
        t.A = (e, t) => {
          const n = e.__vccOpts || e;
          for (const [r, s] of t) n[r] = s;
          return n
        }
      },
      6270: function (e, t, n) {
        var r = n(8407);
        r.__esModule && (r = r.default), "string" === typeof r && (r = [
          [e.id, r, ""]
        ]), r.locals && (e.exports = r.locals);
        var s = n(9548).A;
        s("470d3018", r, !0, {
          sourceMap: !1,
          shadowMode: !1
        })
      },
      5187: function (e, t, n) {
        var r = n(3560);
        r.__esModule && (r = r.default), "string" === typeof r && (r = [
          [e.id, r, ""]
        ]), r.locals && (e.exports = r.locals);
        var s = n(9548).A;
        s("1253ec31", r, !0, {
          sourceMap: !1,
          shadowMode: !1
        })
      },
      578: function (e, t, n) {
        var r = n(1203);
        r.__esModule && (r = r.default), "string" === typeof r && (r = [
          [e.id, r, ""]
        ]), r.locals && (e.exports = r.locals);
        var s = n(9548).A;
        s("486325d0", r, !0, {
          sourceMap: !1,
          shadowMode: !1
        })
      },
      7021: function (e, t, n) {
        var r = n(8354);
        r.__esModule && (r = r.default), "string" === typeof r && (r = [
          [e.id, r, ""]
        ]), r.locals && (e.exports = r.locals);
        var s = n(9548).A;
        s("5d0fb0ce", r, !0, {
          sourceMap: !1,
          shadowMode: !1
        })
      },
      9548: function (e, t, n) {
        "use strict";

        function r(e, t) {
          for (var n = [], r = {}, s = 0; s < t.length; s++) {
            var o = t[s],
              i = o[0],
              a = o[1],
              l = o[2],
              c = o[3],
              u = {
                id: e + ":" + s,
                css: a,
                media: l,
                sourceMap: c
              };
            r[i] ? r[i].parts.push(u) : n.push(r[i] = {
              id: i,
              parts: [u]
            })
          }
          return n
        }
        n.d(t, {
          A: function () {
            return p
          }
        });
        var s = "undefined" !== typeof document;
        if ("undefined" !== typeof DEBUG && DEBUG && !s) throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");
        var o = {},
          i = s && (document.head || document.getElementsByTagName("head")[0]),
          a = null,
          l = 0,
          c = !1,
          u = function () {},
          f = null,
          d = "data-vue-ssr-id",
          h = "undefined" !== typeof navigator && /msie [6-9]\b/.test(navigator.userAgent.toLowerCase());

        function p(e, t, n, s) {
          c = n, f = s || {};
          var i = r(e, t);
          return m(i),
            function (t) {
              for (var n = [], s = 0; s < i.length; s++) {
                var a = i[s],
                  l = o[a.id];
                l.refs--, n.push(l)
              }
              t ? (i = r(e, t), m(i)) : i = [];
              for (s = 0; s < n.length; s++) {
                l = n[s];
                if (0 === l.refs) {
                  for (var c = 0; c < l.parts.length; c++) l.parts[c]();
                  delete o[l.id]
                }
              }
            }
        }

        function m(e) {
          for (var t = 0; t < e.length; t++) {
            var n = e[t],
              r = o[n.id];
            if (r) {
              r.refs++;
              for (var s = 0; s < r.parts.length; s++) r.parts[s](n.parts[s]);
              for (; s < n.parts.length; s++) r.parts.push(v(n.parts[s]));
              r.parts.length > n.parts.length && (r.parts.length = n.parts.length)
            } else {
              var i = [];
              for (s = 0; s < n.parts.length; s++) i.push(v(n.parts[s]));
              o[n.id] = {
                id: n.id,
                refs: 1,
                parts: i
              }
            }
          }
        }

        function g() {
          var e = document.createElement("style");
          return e.type = "text/css", i.appendChild(e), e
        }

        function v(e) {
          var t, n, r = document.querySelector("style[" + d + '~="' + e.id + '"]');
          if (r) {
            if (c) return u;
            r.parentNode.removeChild(r)
          }
          if (h) {
            var s = l++;
            r = a || (a = g()), t = b.bind(null, r, s, !1), n = b.bind(null, r, s, !0)
          } else r = g(), t = S.bind(null, r), n = function () {
            r.parentNode.removeChild(r)
          };
          return t(e),
            function (r) {
              if (r) {
                if (r.css === e.css && r.media === e.media && r.sourceMap === e.sourceMap) return;
                t(e = r)
              } else n()
            }
        }
        var y = function () {
          var e = [];
          return function (t, n) {
            return e[t] = n, e.filter(Boolean).join("\n")
          }
        }();

        function b(e, t, n, r) {
          var s = n ? "" : r.css;
          if (e.styleSheet) e.styleSheet.cssText = y(t, s);
          else {
            var o = document.createTextNode(s),
              i = e.childNodes;
            i[t] && e.removeChild(i[t]), i.length ? e.insertBefore(o, i[t]) : e.appendChild(o)
          }
        }

        function S(e, t) {
          var n = t.css,
            r = t.media,
            s = t.sourceMap;
          if (r && e.setAttribute("media", r), f.ssrId && e.setAttribute(d, t.id), s && (n += "\n/*# sourceURL=" + s.sources[0] + " */", n += "\n/*# sourceMappingURL=data:application/json;base64," + btoa(unescape(encodeURIComponent(JSON.stringify(s)))) + " */"), e.styleSheet) e.styleSheet.cssText = n;
          else {
            while (e.firstChild) e.removeChild(e.firstChild);
            e.appendChild(document.createTextNode(n))
          }
        }
      },
      9306: function (e, t, n) {
        "use strict";
        var r = n(4901),
          s = n(6823),
          o = TypeError;
        e.exports = function (e) {
          if (r(e)) return e;
          throw new o(s(e) + " is not a function")
        }
      },
      8551: function (e, t, n) {
        "use strict";
        var r = n(34),
          s = String,
          o = TypeError;
        e.exports = function (e) {
          if (r(e)) return e;
          throw new o(s(e) + " is not an object")
        }
      },
      9617: function (e, t, n) {
        "use strict";
        var r = n(5397),
          s = n(5610),
          o = n(6198),
          i = function (e) {
            return function (t, n, i) {
              var a = r(t),
                l = o(a);
              if (0 === l) return !e && -1;
              var c, u = s(i, l);
              if (e && n !== n) {
                while (l > u)
                  if (c = a[u++], c !== c) return !0
              } else
                for (; l > u; u++)
                  if ((e || u in a) && a[u] === n) return e || u || 0;
              return !e && -1
            }
          };
        e.exports = {
          includes: i(!0),
          indexOf: i(!1)
        }
      },
      4527: function (e, t, n) {
        "use strict";
        var r = n(3724),
          s = n(4376),
          o = TypeError,
          i = Object.getOwnPropertyDescriptor,
          a = r && ! function () {
            if (void 0 !== this) return !0;
            try {
              Object.defineProperty([], "length", {
                writable: !1
              }).length = 1
            } catch (e) {
              return e instanceof TypeError
            }
          }();
        e.exports = a ? function (e, t) {
          if (s(e) && !i(e, "length").writable) throw new o("Cannot set read only .length");
          return e.length = t
        } : function (e, t) {
          return e.length = t
        }
      },
      2195: function (e, t, n) {
        "use strict";
        var r = n(9504),
          s = r({}.toString),
          o = r("".slice);
        e.exports = function (e) {
          return o(s(e), 8, -1)
        }
      },
      6955: function (e, t, n) {
        "use strict";
        var r = n(2140),
          s = n(4901),
          o = n(2195),
          i = n(8227),
          a = i("toStringTag"),
          l = Object,
          c = "Arguments" === o(function () {
            return arguments
          }()),
          u = function (e, t) {
            try {
              return e[t]
            } catch (n) {}
          };
        e.exports = r ? o : function (e) {
          var t, n, r;
          return void 0 === e ? "Undefined" : null === e ? "Null" : "string" == typeof (n = u(t = l(e), a)) ? n : c ? o(t) : "Object" === (r = o(t)) && s(t.callee) ? "Arguments" : r
        }
      },
      7740: function (e, t, n) {
        "use strict";
        var r = n(9297),
          s = n(5031),
          o = n(7347),
          i = n(4913);
        e.exports = function (e, t, n) {
          for (var a = s(t), l = i.f, c = o.f, u = 0; u < a.length; u++) {
            var f = a[u];
            r(e, f) || n && r(n, f) || l(e, f, c(t, f))
          }
        }
      },
      6699: function (e, t, n) {
        "use strict";
        var r = n(3724),
          s = n(4913),
          o = n(6980);
        e.exports = r ? function (e, t, n) {
          return s.f(e, t, o(1, n))
        } : function (e, t, n) {
          return e[t] = n, e
        }
      },
      6980: function (e) {
        "use strict";
        e.exports = function (e, t) {
          return {
            enumerable: !(1 & e),
            configurable: !(2 & e),
            writable: !(4 & e),
            value: t
          }
        }
      },
      2106: function (e, t, n) {
        "use strict";
        var r = n(283),
          s = n(4913);
        e.exports = function (e, t, n) {
          return n.get && r(n.get, t, {
            getter: !0
          }), n.set && r(n.set, t, {
            setter: !0
          }), s.f(e, t, n)
        }
      },
      6840: function (e, t, n) {
        "use strict";
        var r = n(4901),
          s = n(4913),
          o = n(283),
          i = n(9433);
        e.exports = function (e, t, n, a) {
          a || (a = {});
          var l = a.enumerable,
            c = void 0 !== a.name ? a.name : t;
          if (r(n) && o(n, c, a), a.global) l ? e[t] = n : i(t, n);
          else {
            try {
              a.unsafe ? e[t] && (l = !0) : delete e[t]
            } catch (u) {}
            l ? e[t] = n : s.f(e, t, {
              value: n,
              enumerable: !1,
              configurable: !a.nonConfigurable,
              writable: !a.nonWritable
            })
          }
          return e
        }
      },
      9433: function (e, t, n) {
        "use strict";
        var r = n(4576),
          s = Object.defineProperty;
        e.exports = function (e, t) {
          try {
            s(r, e, {
              value: t,
              configurable: !0,
              writable: !0
            })
          } catch (n) {
            r[e] = t
          }
          return t
        }
      },
      3724: function (e, t, n) {
        "use strict";
        var r = n(9039);
        e.exports = !r((function () {
          return 7 !== Object.defineProperty({}, 1, {
            get: function () {
              return 7
            }
          })[1]
        }))
      },
      4055: function (e, t, n) {
        "use strict";
        var r = n(4576),
          s = n(34),
          o = r.document,
          i = s(o) && s(o.createElement);
        e.exports = function (e) {
          return i ? o.createElement(e) : {}
        }
      },
      6837: function (e) {
        "use strict";
        var t = TypeError,
          n = 9007199254740991;
        e.exports = function (e) {
          if (e > n) throw t("Maximum allowed index exceeded");
          return e
        }
      },
      8727: function (e) {
        "use strict";
        e.exports = ["constructor", "hasOwnProperty", "isPrototypeOf", "propertyIsEnumerable", "toLocaleString", "toString", "valueOf"]
      },
      2839: function (e, t, n) {
        "use strict";
        var r = n(4576),
          s = r.navigator,
          o = s && s.userAgent;
        e.exports = o ? String(o) : ""
      },
      9519: function (e, t, n) {
        "use strict";
        var r, s, o = n(4576),
          i = n(2839),
          a = o.process,
          l = o.Deno,
          c = a && a.versions || l && l.version,
          u = c && c.v8;
        u && (r = u.split("."), s = r[0] > 0 && r[0] < 4 ? 1 : +(r[0] + r[1])), !s && i && (r = i.match(/Edge\/(\d+)/), (!r || r[1] >= 74) && (r = i.match(/Chrome\/(\d+)/), r && (s = +r[1]))), e.exports = s
      },
      6518: function (e, t, n) {
        "use strict";
        var r = n(4576),
          s = n(7347).f,
          o = n(6699),
          i = n(6840),
          a = n(9433),
          l = n(7740),
          c = n(2796);
        e.exports = function (e, t) {
          var n, u, f, d, h, p, m = e.target,
            g = e.global,
            v = e.stat;
          if (u = g ? r : v ? r[m] || a(m, {}) : r[m] && r[m].prototype, u)
            for (f in t) {
              if (h = t[f], e.dontCallGetSet ? (p = s(u, f), d = p && p.value) : d = u[f], n = c(g ? f : m + (v ? "." : "#") + f, e.forced), !n && void 0 !== d) {
                if (typeof h == typeof d) continue;
                l(h, d)
              }(e.sham || d && d.sham) && o(h, "sham", !0), i(u, f, h, e)
            }
        }
      },
      9039: function (e) {
        "use strict";
        e.exports = function (e) {
          try {
            return !!e()
          } catch (t) {
            return !0
          }
        }
      },
      616: function (e, t, n) {
        "use strict";
        var r = n(9039);
        e.exports = !r((function () {
          var e = function () {}.bind();
          return "function" != typeof e || e.hasOwnProperty("prototype")
        }))
      },
      9565: function (e, t, n) {
        "use strict";
        var r = n(616),
          s = Function.prototype.call;
        e.exports = r ? s.bind(s) : function () {
          return s.apply(s, arguments)
        }
      },
      350: function (e, t, n) {
        "use strict";
        var r = n(3724),
          s = n(9297),
          o = Function.prototype,
          i = r && Object.getOwnPropertyDescriptor,
          a = s(o, "name"),
          l = a && "something" === function () {}.name,
          c = a && (!r || r && i(o, "name").configurable);
        e.exports = {
          EXISTS: a,
          PROPER: l,
          CONFIGURABLE: c
        }
      },
      9504: function (e, t, n) {
        "use strict";
        var r = n(616),
          s = Function.prototype,
          o = s.call,
          i = r && s.bind.bind(o, o);
        e.exports = r ? i : function (e) {
          return function () {
            return o.apply(e, arguments)
          }
        }
      },
      7751: function (e, t, n) {
        "use strict";
        var r = n(4576),
          s = n(4901),
          o = function (e) {
            return s(e) ? e : void 0
          };
        e.exports = function (e, t) {
          return arguments.length < 2 ? o(r[e]) : r[e] && r[e][t]
        }
      },
      5966: function (e, t, n) {
        "use strict";
        var r = n(9306),
          s = n(4117);
        e.exports = function (e, t) {
          var n = e[t];
          return s(n) ? void 0 : r(n)
        }
      },
      4576: function (e, t, n) {
        "use strict";
        var r = function (e) {
          return e && e.Math === Math && e
        };
        e.exports = r("object" == typeof globalThis && globalThis) || r("object" == typeof window && window) || r("object" == typeof self && self) || r("object" == typeof n.g && n.g) || r("object" == typeof this && this) || function () {
          return this
        }() || Function("return this")()
      },
      9297: function (e, t, n) {
        "use strict";
        var r = n(9504),
          s = n(8981),
          o = r({}.hasOwnProperty);
        e.exports = Object.hasOwn || function (e, t) {
          return o(s(e), t)
        }
      },
      421: function (e) {
        "use strict";
        e.exports = {}
      },
      5917: function (e, t, n) {
        "use strict";
        var r = n(3724),
          s = n(9039),
          o = n(4055);
        e.exports = !r && !s((function () {
          return 7 !== Object.defineProperty(o("div"), "a", {
            get: function () {
              return 7
            }
          }).a
        }))
      },
      7055: function (e, t, n) {
        "use strict";
        var r = n(9504),
          s = n(9039),
          o = n(2195),
          i = Object,
          a = r("".split);
        e.exports = s((function () {
          return !i("z").propertyIsEnumerable(0)
        })) ? function (e) {
          return "String" === o(e) ? a(e, "") : i(e)
        } : i
      },
      3706: function (e, t, n) {
        "use strict";
        var r = n(9504),
          s = n(4901),
          o = n(7629),
          i = r(Function.toString);
        s(o.inspectSource) || (o.inspectSource = function (e) {
          return i(e)
        }), e.exports = o.inspectSource
      },
      1181: function (e, t, n) {
        "use strict";
        var r, s, o, i = n(8622),
          a = n(4576),
          l = n(34),
          c = n(6699),
          u = n(9297),
          f = n(7629),
          d = n(6119),
          h = n(421),
          p = "Object already initialized",
          m = a.TypeError,
          g = a.WeakMap,
          v = function (e) {
            return o(e) ? s(e) : r(e, {})
          },
          y = function (e) {
            return function (t) {
              var n;
              if (!l(t) || (n = s(t)).type !== e) throw new m("Incompatible receiver, " + e + " required");
              return n
            }
          };
        if (i || f.state) {
          var b = f.state || (f.state = new g);
          b.get = b.get, b.has = b.has, b.set = b.set, r = function (e, t) {
            if (b.has(e)) throw new m(p);
            return t.facade = e, b.set(e, t), t
          }, s = function (e) {
            return b.get(e) || {}
          }, o = function (e) {
            return b.has(e)
          }
        } else {
          var S = d("state");
          h[S] = !0, r = function (e, t) {
            if (u(e, S)) throw new m(p);
            return t.facade = e, c(e, S, t), t
          }, s = function (e) {
            return u(e, S) ? e[S] : {}
          }, o = function (e) {
            return u(e, S)
          }
        }
        e.exports = {
          set: r,
          get: s,
          has: o,
          enforce: v,
          getterFor: y
        }
      },
      4376: function (e, t, n) {
        "use strict";
        var r = n(2195);
        e.exports = Array.isArray || function (e) {
          return "Array" === r(e)
        }
      },
      4901: function (e) {
        "use strict";
        var t = "object" == typeof document && document.all;
        e.exports = "undefined" == typeof t && void 0 !== t ? function (e) {
          return "function" == typeof e || e === t
        } : function (e) {
          return "function" == typeof e
        }
      },
      2796: function (e, t, n) {
        "use strict";
        var r = n(9039),
          s = n(4901),
          o = /#|\.prototype\./,
          i = function (e, t) {
            var n = l[a(e)];
            return n === u || n !== c && (s(t) ? r(t) : !!t)
          },
          a = i.normalize = function (e) {
            return String(e).replace(o, ".").toLowerCase()
          },
          l = i.data = {},
          c = i.NATIVE = "N",
          u = i.POLYFILL = "P";
        e.exports = i
      },
      4117: function (e) {
        "use strict";
        e.exports = function (e) {
          return null === e || void 0 === e
        }
      },
      34: function (e, t, n) {
        "use strict";
        var r = n(4901);
        e.exports = function (e) {
          return "object" == typeof e ? null !== e : r(e)
        }
      },
      6395: function (e) {
        "use strict";
        e.exports = !1
      },
      757: function (e, t, n) {
        "use strict";
        var r = n(7751),
          s = n(4901),
          o = n(1625),
          i = n(7040),
          a = Object;
        e.exports = i ? function (e) {
          return "symbol" == typeof e
        } : function (e) {
          var t = r("Symbol");
          return s(t) && o(t.prototype, a(e))
        }
      },
      6198: function (e, t, n) {
        "use strict";
        var r = n(8014);
        e.exports = function (e) {
          return r(e.length)
        }
      },
      283: function (e, t, n) {
        "use strict";
        var r = n(9504),
          s = n(9039),
          o = n(4901),
          i = n(9297),
          a = n(3724),
          l = n(350).CONFIGURABLE,
          c = n(3706),
          u = n(1181),
          f = u.enforce,
          d = u.get,
          h = String,
          p = Object.defineProperty,
          m = r("".slice),
          g = r("".replace),
          v = r([].join),
          y = a && !s((function () {
            return 8 !== p((function () {}), "length", {
              value: 8
            }).length
          })),
          b = String(String).split("String"),
          S = e.exports = function (e, t, n) {
            "Symbol(" === m(h(t), 0, 7) && (t = "[" + g(h(t), /^Symbol\(([^)]*)\).*$/, "$1") + "]"), n && n.getter && (t = "get " + t), n && n.setter && (t = "set " + t), (!i(e, "name") || l && e.name !== t) && (a ? p(e, "name", {
              value: t,
              configurable: !0
            }) : e.name = t), y && n && i(n, "arity") && e.length !== n.arity && p(e, "length", {
              value: n.arity
            });
            try {
              n && i(n, "constructor") && n.constructor ? a && p(e, "prototype", {
                writable: !1
              }) : e.prototype && (e.prototype = void 0)
            } catch (s) {}
            var r = f(e);
            return i(r, "source") || (r.source = v(b, "string" == typeof t ? t : "")), e
          };
        Function.prototype.toString = S((function () {
          return o(this) && d(this).source || c(this)
        }), "toString")
      },
      741: function (e) {
        "use strict";
        var t = Math.ceil,
          n = Math.floor;
        e.exports = Math.trunc || function (e) {
          var r = +e;
          return (r > 0 ? n : t)(r)
        }
      },
      4913: function (e, t, n) {
        "use strict";
        var r = n(3724),
          s = n(5917),
          o = n(8686),
          i = n(8551),
          a = n(6969),
          l = TypeError,
          c = Object.defineProperty,
          u = Object.getOwnPropertyDescriptor,
          f = "enumerable",
          d = "configurable",
          h = "writable";
        t.f = r ? o ? function (e, t, n) {
          if (i(e), t = a(t), i(n), "function" === typeof e && "prototype" === t && "value" in n && h in n && !n[h]) {
            var r = u(e, t);
            r && r[h] && (e[t] = n.value, n = {
              configurable: d in n ? n[d] : r[d],
              enumerable: f in n ? n[f] : r[f],
              writable: !1
            })
          }
          return c(e, t, n)
        } : c : function (e, t, n) {
          if (i(e), t = a(t), i(n), s) try {
            return c(e, t, n)
          } catch (r) {}
          if ("get" in n || "set" in n) throw new l("Accessors not supported");
          return "value" in n && (e[t] = n.value), e
        }
      },
      7347: function (e, t, n) {
        "use strict";
        var r = n(3724),
          s = n(9565),
          o = n(8773),
          i = n(6980),
          a = n(5397),
          l = n(6969),
          c = n(9297),
          u = n(5917),
          f = Object.getOwnPropertyDescriptor;
        t.f = r ? f : function (e, t) {
          if (e = a(e), t = l(t), u) try {
            return f(e, t)
          } catch (n) {}
          if (c(e, t)) return i(!s(o.f, e, t), e[t])
        }
      },
      8480: function (e, t, n) {
        "use strict";
        var r = n(1828),
          s = n(8727),
          o = s.concat("length", "prototype");
        t.f = Object.getOwnPropertyNames || function (e) {
          return r(e, o)
        }
      },
      3717: function (e, t) {
        "use strict";
        t.f = Object.getOwnPropertySymbols
      },
      1625: function (e, t, n) {
        "use strict";
        var r = n(9504);
        e.exports = r({}.isPrototypeOf)
      },
      1828: function (e, t, n) {
        "use strict";
        var r = n(9504),
          s = n(9297),
          o = n(5397),
          i = n(9617).indexOf,
          a = n(421),
          l = r([].push);
        e.exports = function (e, t) {
          var n, r = o(e),
            c = 0,
            u = [];
          for (n in r) !s(a, n) && s(r, n) && l(u, n);
          while (t.length > c) s(r, n = t[c++]) && (~i(u, n) || l(u, n));
          return u
        }
      },
      8773: function (e, t) {
        "use strict";
        var n = {}.propertyIsEnumerable,
          r = Object.getOwnPropertyDescriptor,
          s = r && !n.call({
            1: 2
          }, 1);
        t.f = s ? function (e) {
          var t = r(this, e);
          return !!t && t.enumerable
        } : n
      },
      4270: function (e, t, n) {
        "use strict";
        var r = n(9565),
          s = n(4901),
          o = n(34),
          i = TypeError;
        e.exports = function (e, t) {
          var n, a;
          if ("string" === t && s(n = e.toString) && !o(a = r(n, e))) return a;
          if (s(n = e.valueOf) && !o(a = r(n, e))) return a;
          if ("string" !== t && s(n = e.toString) && !o(a = r(n, e))) return a;
          throw new i("Can't convert object to primitive value")
        }
      },
      5031: function (e, t, n) {
        "use strict";
        var r = n(7751),
          s = n(9504),
          o = n(8480),
          i = n(3717),
          a = n(8551),
          l = s([].concat);
        e.exports = r("Reflect", "ownKeys") || function (e) {
          var t = o.f(a(e)),
            n = i.f;
          return n ? l(t, n(e)) : t
        }
      },
      7750: function (e, t, n) {
        "use strict";
        var r = n(4117),
          s = TypeError;
        e.exports = function (e) {
          if (r(e)) throw new s("Can't call method on " + e);
          return e
        }
      },
      6119: function (e, t, n) {
        "use strict";
        var r = n(5745),
          s = n(3392),
          o = r("keys");
        e.exports = function (e) {
          return o[e] || (o[e] = s(e))
        }
      },
      7629: function (e, t, n) {
        "use strict";
        var r = n(6395),
          s = n(4576),
          o = n(9433),
          i = "__core-js_shared__",
          a = e.exports = s[i] || o(i, {});
        (a.versions || (a.versions = [])).push({
          version: "3.38.1",
          mode: r ? "pure" : "global",
          copyright: "© 2014-2024 Denis Pushkarev (zloirock.ru)",
          license: "https://github.com/zloirock/core-js/blob/v3.38.1/LICENSE",
          source: "https://github.com/zloirock/core-js"
        })
      },
      5745: function (e, t, n) {
        "use strict";
        var r = n(7629);
        e.exports = function (e, t) {
          return r[e] || (r[e] = t || {})
        }
      },
      4495: function (e, t, n) {
        "use strict";
        var r = n(9519),
          s = n(9039),
          o = n(4576),
          i = o.String;
        e.exports = !!Object.getOwnPropertySymbols && !s((function () {
          var e = Symbol("symbol detection");
          return !i(e) || !(Object(e) instanceof Symbol) || !Symbol.sham && r && r < 41
        }))
      },
      5610: function (e, t, n) {
        "use strict";
        var r = n(1291),
          s = Math.max,
          o = Math.min;
        e.exports = function (e, t) {
          var n = r(e);
          return n < 0 ? s(n + t, 0) : o(n, t)
        }
      },
      5397: function (e, t, n) {
        "use strict";
        var r = n(7055),
          s = n(7750);
        e.exports = function (e) {
          return r(s(e))
        }
      },
      1291: function (e, t, n) {
        "use strict";
        var r = n(741);
        e.exports = function (e) {
          var t = +e;
          return t !== t || 0 === t ? 0 : r(t)
        }
      },
      8014: function (e, t, n) {
        "use strict";
        var r = n(1291),
          s = Math.min;
        e.exports = function (e) {
          var t = r(e);
          return t > 0 ? s(t, 9007199254740991) : 0
        }
      },
      8981: function (e, t, n) {
        "use strict";
        var r = n(7750),
          s = Object;
        e.exports = function (e) {
          return s(r(e))
        }
      },
      2777: function (e, t, n) {
        "use strict";
        var r = n(9565),
          s = n(34),
          o = n(757),
          i = n(5966),
          a = n(4270),
          l = n(8227),
          c = TypeError,
          u = l("toPrimitive");
        e.exports = function (e, t) {
          if (!s(e) || o(e)) return e;
          var n, l = i(e, u);
          if (l) {
            if (void 0 === t && (t = "default"), n = r(l, e, t), !s(n) || o(n)) return n;
            throw new c("Can't convert object to primitive value")
          }
          return void 0 === t && (t = "number"), a(e, t)
        }
      },
      6969: function (e, t, n) {
        "use strict";
        var r = n(2777),
          s = n(757);
        e.exports = function (e) {
          var t = r(e, "string");
          return s(t) ? t : t + ""
        }
      },
      2140: function (e, t, n) {
        "use strict";
        var r = n(8227),
          s = r("toStringTag"),
          o = {};
        o[s] = "z", e.exports = "[object z]" === String(o)
      },
      655: function (e, t, n) {
        "use strict";
        var r = n(6955),
          s = String;
        e.exports = function (e) {
          if ("Symbol" === r(e)) throw new TypeError("Cannot convert a Symbol value to a string");
          return s(e)
        }
      },
      6823: function (e) {
        "use strict";
        var t = String;
        e.exports = function (e) {
          try {
            return t(e)
          } catch (n) {
            return "Object"
          }
        }
      },
      3392: function (e, t, n) {
        "use strict";
        var r = n(9504),
          s = 0,
          o = Math.random(),
          i = r(1..toString);
        e.exports = function (e) {
          return "Symbol(" + (void 0 === e ? "" : e) + ")_" + i(++s + o, 36)
        }
      },
      7040: function (e, t, n) {
        "use strict";
        var r = n(4495);
        e.exports = r && !Symbol.sham && "symbol" == typeof Symbol.iterator
      },
      8686: function (e, t, n) {
        "use strict";
        var r = n(3724),
          s = n(9039);
        e.exports = r && s((function () {
          return 42 !== Object.defineProperty((function () {}), "prototype", {
            value: 42,
            writable: !1
          }).prototype
        }))
      },
      2812: function (e) {
        "use strict";
        var t = TypeError;
        e.exports = function (e, n) {
          if (e < n) throw new t("Not enough arguments");
          return e
        }
      },
      8622: function (e, t, n) {
        "use strict";
        var r = n(4576),
          s = n(4901),
          o = r.WeakMap;
        e.exports = s(o) && /native code/.test(String(o))
      },
      8227: function (e, t, n) {
        "use strict";
        var r = n(4576),
          s = n(5745),
          o = n(9297),
          i = n(3392),
          a = n(4495),
          l = n(7040),
          c = r.Symbol,
          u = s("wks"),
          f = l ? c["for"] || c : c && c.withoutSetter || i;
        e.exports = function (e) {
          return o(u, e) || (u[e] = a && o(c, e) ? c[e] : f("Symbol." + e)), u[e]
        }
      },
      4114: function (e, t, n) {
        "use strict";
        var r = n(6518),
          s = n(8981),
          o = n(6198),
          i = n(4527),
          a = n(6837),
          l = n(9039),
          c = l((function () {
            return 4294967297 !== [].push.call({
              length: 4294967296
            }, 1)
          })),
          u = function () {
            try {
              Object.defineProperty([], "length", {
                writable: !1
              }).push()
            } catch (e) {
              return e instanceof TypeError
            }
          },
          f = c || !u();
        r({
          target: "Array",
          proto: !0,
          arity: 1,
          forced: f
        }, {
          push: function (e) {
            var t = s(this),
              n = o(t),
              r = arguments.length;
            a(n + r);
            for (var l = 0; l < r; l++) t[n] = arguments[l], n++;
            return i(t, n), n
          }
        })
      },
      4603: function (e, t, n) {
        "use strict";
        var r = n(6840),
          s = n(9504),
          o = n(655),
          i = n(2812),
          a = URLSearchParams,
          l = a.prototype,
          c = s(l.append),
          u = s(l["delete"]),
          f = s(l.forEach),
          d = s([].push),
          h = new a("a=1&a=2&b=3");
        h["delete"]("a", 1), h["delete"]("b", void 0), h + "" !== "a=2" && r(l, "delete", (function (e) {
          var t = arguments.length,
            n = t < 2 ? void 0 : arguments[1];
          if (t && void 0 === n) return u(this, e);
          var r = [];
          f(this, (function (e, t) {
            d(r, {
              key: t,
              value: e
            })
          })), i(t, 1);
          var s, a = o(e),
            l = o(n),
            h = 0,
            p = 0,
            m = !1,
            g = r.length;
          while (h < g) s = r[h++], m || s.key === a ? (m = !0, u(this, s.key)) : p++;
          while (p < g) s = r[p++], s.key === a && s.value === l || c(this, s.key, s.value)
        }), {
          enumerable: !0,
          unsafe: !0
        })
      },
      7566: function (e, t, n) {
        "use strict";
        var r = n(6840),
          s = n(9504),
          o = n(655),
          i = n(2812),
          a = URLSearchParams,
          l = a.prototype,
          c = s(l.getAll),
          u = s(l.has),
          f = new a("a=1");
        !f.has("a", 2) && f.has("a", void 0) || r(l, "has", (function (e) {
          var t = arguments.length,
            n = t < 2 ? void 0 : arguments[1];
          if (t && void 0 === n) return u(this, e);
          var r = c(this, e);
          i(t, 1);
          var s = o(n),
            a = 0;
          while (a < r.length)
            if (r[a++] === s) return !0;
          return !1
        }), {
          enumerable: !0,
          unsafe: !0
        })
      },
      8721: function (e, t, n) {
        "use strict";
        var r = n(3724),
          s = n(9504),
          o = n(2106),
          i = URLSearchParams.prototype,
          a = s(i.forEach);
        r && !("size" in i) && o(i, "size", {
          get: function () {
            var e = 0;
            return a(this, (function () {
              e++
            })), e
          },
          configurable: !0,
          enumerable: !0
        })
      }
    },
    t = {};

  function n(r) {
    var s = t[r];
    if (void 0 !== s) return s.exports;
    var o = t[r] = {
      id: r,
      exports: {}
    };
    return e[r].call(o.exports, o, o.exports, n), o.exports
  }
  n.m = e,
    function () {
      n.n = function (e) {
        var t = e && e.__esModule ? function () {
          return e["default"]
        } : function () {
          return e
        };
        return n.d(t, {
          a: t
        }), t
      }
    }(),
    function () {
      n.d = function (e, t) {
        for (var r in t) n.o(t, r) && !n.o(e, r) && Object.defineProperty(e, r, {
          enumerable: !0,
          get: t[r]
        })
      }
    }(),
    function () {
      n.f = {}, n.e = function (e) {
        return Promise.all(Object.keys(n.f).reduce((function (t, r) {
          return n.f[r](e, t), t
        }), []))
      }
    }(),
    function () {
      n.u = function (e) {
        return e + ".js"
      }
    }(),
    function () {
      n.g = function () {
        if ("object" === typeof globalThis) return globalThis;
        try {
          return this || new Function("return this")()
        } catch (e) {
          if ("object" === typeof window) return window
        }
      }()
    }(),
    function () {
      n.o = function (e, t) {
        return Object.prototype.hasOwnProperty.call(e, t)
      }
    }(),
    function () {
      var e = {},
        t = "tester-v3:";
      n.l = function (r, s, o, i) {
        if (e[r]) e[r].push(s);
        else {
          var a, l;
          if (void 0 !== o)
            for (var c = document.getElementsByTagName("script"), u = 0; u < c.length; u++) {
              var f = c[u];
              if (f.getAttribute("src") == r || f.getAttribute("data-webpack") == t + o) {
                a = f;
                break
              }
            }
          a || (l = !0, a = document.createElement("script"), a.charset = "utf-8", a.timeout = 120, n.nc && a.setAttribute("nonce", n.nc), a.setAttribute("data-webpack", t + o), a.src = r), e[r] = [s];
          var d = function (t, n) {
              a.onerror = a.onload = null, clearTimeout(h);
              var s = e[r];
              if (delete e[r], a.parentNode && a.parentNode.removeChild(a), s && s.forEach((function (e) {
                  return e(n)
                })), t) return t(n)
            },
            h = setTimeout(d.bind(null, void 0, {
              type: "timeout",
              target: a
            }), 12e4);
          a.onerror = d.bind(null, a.onerror), a.onload = d.bind(null, a.onload), l && document.head.appendChild(a)
        }
      }
    }(),
    function () {
      n.r = function (e) {
        "undefined" !== typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {
          value: "Module"
        }), Object.defineProperty(e, "__esModule", {
          value: !0
        })
      }
    }(),
    function () {
      n.p = "/"
    }(),
    function () {
      var e = {
        887: 0
      };
      n.f.j = function (t, r) {
        var s = n.o(e, t) ? e[t] : void 0;
        if (0 !== s)
          if (s) r.push(s[2]);
          else {
            var o = new Promise((function (n, r) {
              s = e[t] = [n, r]
            }));
            r.push(s[2] = o);
            var i = n.p + n.u(t),
              a = new Error,
              l = function (r) {
                if (n.o(e, t) && (s = e[t], 0 !== s && (e[t] = void 0), s)) {
                  var o = r && ("load" === r.type ? "missing" : r.type),
                    i = r && r.target && r.target.src;
                  a.message = "Loading chunk " + t + " failed.\n(" + o + ": " + i + ")", a.name = "ChunkLoadError", a.type = o, a.request = i, s[1](a)
                }
              };
            n.l(i, l, "chunk-" + t, t)
          }
      };
      var t = function (t, r) {
          var s, o, i = r[0],
            a = r[1],
            l = r[2],
            c = 0;
          if (i.some((function (t) {
              return 0 !== e[t]
            }))) {
            for (s in a) n.o(a, s) && (n.m[s] = a[s]);
            if (l) l(n)
          }
          for (t && t(r); c < i.length; c++) o = i[c], n.o(e, o) && e[o] && e[o][0](), e[o] = 0
        },
        r = self["webpackChunktester_v3"] = self["webpackChunktester_v3"] || [];
      r.forEach(t.bind(null, 0)), r.push = t.bind(null, r.push.bind(r))
    }();
  ! function () {
    "use strict";
    var e = {};
    n.r(e), n.d(e, {
      hasBrowserEnv: function () {
        return eo
      },
      hasStandardBrowserEnv: function () {
        return no
      },
      hasStandardBrowserWebWorkerEnv: function () {
        return ro
      },
      navigator: function () {
        return to
      },
      origin: function () {
        return so
      }
    });
    var t = n(641),
      r = n(33),
      s = n(953);
    /**
     * @vue/runtime-dom v3.5.12
     * (c) 2018-present Yuxi (Evan) You and Vue contributors
     * @license MIT
     **/
    let o;
    const i = "undefined" !== typeof window && window.trustedTypes;
    if (i) try {
      o = i.createPolicy("vue", {
        createHTML: e => e
      })
    } catch (ka) {}
    const a = o ? e => o.createHTML(e) : e => e,
      l = "http://www.w3.org/2000/svg",
      c = "http://www.w3.org/1998/Math/MathML",
      u = "undefined" !== typeof document ? document : null,
      f = u && u.createElement("template"),
      d = {
        insert: (e, t, n) => {
          t.insertBefore(e, n || null)
        },
        remove: e => {
          const t = e.parentNode;
          t && t.removeChild(e)
        },
        createElement: (e, t, n, r) => {
          const s = "svg" === t ? u.createElementNS(l, e) : "mathml" === t ? u.createElementNS(c, e) : n ? u.createElement(e, {
            is: n
          }) : u.createElement(e);
          return "select" === e && r && null != r.multiple && s.setAttribute("multiple", r.multiple), s
        },
        createText: e => u.createTextNode(e),
        createComment: e => u.createComment(e),
        setText: (e, t) => {
          e.nodeValue = t
        },
        setElementText: (e, t) => {
          e.textContent = t
        },
        parentNode: e => e.parentNode,
        nextSibling: e => e.nextSibling,
        querySelector: e => u.querySelector(e),
        setScopeId(e, t) {
          e.setAttribute(t, "")
        },
        insertStaticContent(e, t, n, r, s, o) {
          const i = n ? n.previousSibling : t.lastChild;
          if (s && (s === o || s.nextSibling)) {
            while (1)
              if (t.insertBefore(s.cloneNode(!0), n), s === o || !(s = s.nextSibling)) break
          } else {
            f.innerHTML = a("svg" === r ? `<svg>${e}</svg>` : "mathml" === r ? `<math>${e}</math>` : e);
            const s = f.content;
            if ("svg" === r || "mathml" === r) {
              const e = s.firstChild;
              while (e.firstChild) s.appendChild(e.firstChild);
              s.removeChild(e)
            }
            t.insertBefore(s, n)
          }
          return [i ? i.nextSibling : t.firstChild, n ? n.previousSibling : t.lastChild]
        }
      },
      h = Symbol("_vtc"),
      p = {
        name: String,
        type: String,
        css: {
          type: Boolean,
          default: !0
        },
        duration: [String, Number, Object],
        enterFromClass: String,
        enterActiveClass: String,
        enterToClass: String,
        appearFromClass: String,
        appearActiveClass: String,
        appearToClass: String,
        leaveFromClass: String,
        leaveActiveClass: String,
        leaveToClass: String
      };
    t.QP;

    function m(e, t, n) {
      const r = e[h];
      r && (t = (t ? [t, ...r] : [...r]).join(" ")), null == t ? e.removeAttribute("class") : n ? e.setAttribute("class", t) : e.className = t
    }
    const g = Symbol("_vod"),
      v = Symbol("_vsh");
    const y = Symbol("");
    const b = /(^|;)\s*display\s*:/;

    function S(e, t, n) {
      const s = e.style,
        o = (0, r.Kg)(n);
      let i = !1;
      if (n && !o) {
        if (t)
          if ((0, r.Kg)(t))
            for (const e of t.split(";")) {
              const t = e.slice(0, e.indexOf(":")).trim();
              null == n[t] && k(s, t, "")
            } else
              for (const e in t) null == n[e] && k(s, e, "");
        for (const e in n) "display" === e && (i = !0), k(s, e, n[e])
      } else if (o) {
        if (t !== n) {
          const e = s[y];
          e && (n += ";" + e), s.cssText = n, i = b.test(n)
        }
      } else t && e.removeAttribute("style");
      g in e && (e[g] = i ? s.display : "", e[v] && (s.display = "none"))
    }
    const w = /\s*!important$/;

    function k(e, t, n) {
      if ((0, r.cy)(n)) n.forEach((n => k(e, t, n)));
      else if (null == n && (n = ""), t.startsWith("--")) e.setProperty(t, n);
      else {
        const s = E(e, t);
        w.test(n) ? e.setProperty((0, r.Tg)(s), n.replace(w, ""), "important") : e[s] = n
      }
    }
    const x = ["Webkit", "Moz", "ms"],
      _ = {};

    function E(e, t) {
      const n = _[t];
      if (n) return n;
      let s = (0, r.PT)(t);
      if ("filter" !== s && s in e) return _[t] = s;
      s = (0, r.ZH)(s);
      for (let r = 0; r < x.length; r++) {
        const n = x[r] + s;
        if (n in e) return _[t] = n
      }
      return t
    }
    const T = "http://www.w3.org/1999/xlink";

    function C(e, t, n, s, o, i = (0, r.J$)(t)) {
      s && t.startsWith("xlink:") ? null == n ? e.removeAttributeNS(T, t.slice(6, t.length)) : e.setAttributeNS(T, t, n) : null == n || i && !(0, r.Y2)(n) ? e.removeAttribute(t) : e.setAttribute(t, i ? "" : (0, r.Bm)(n) ? String(n) : n)
    }

    function O(e, t, n, s, o) {
      if ("innerHTML" === t || "textContent" === t) return void(null != n && (e[t] = "innerHTML" === t ? a(n) : n));
      const i = e.tagName;
      if ("value" === t && "PROGRESS" !== i && !i.includes("-")) {
        const r = "OPTION" === i ? e.getAttribute("value") || "" : e.value,
          s = null == n ? "checkbox" === e.type ? "on" : "" : String(n);
        return r === s && "_value" in e || (e.value = s), null == n && e.removeAttribute(t), void(e._value = n)
      }
      let l = !1;
      if ("" === n || null == n) {
        const s = typeof e[t];
        "boolean" === s ? n = (0, r.Y2)(n) : null == n && "string" === s ? (n = "", l = !0) : "number" === s && (n = 0, l = !0)
      }
      try {
        e[t] = n
      } catch (ka) {
        0
      }
      l && e.removeAttribute(o || t)
    }

    function R(e, t, n, r) {
      e.addEventListener(t, n, r)
    }

    function F(e, t, n, r) {
      e.removeEventListener(t, n, r)
    }
    const L = Symbol("_vei");

    function P(e, t, n, r, s = null) {
      const o = e[L] || (e[L] = {}),
        i = o[t];
      if (r && i) i.value = r;
      else {
        const [n, a] = A(t);
        if (r) {
          const i = o[t] = D(r, s);
          R(e, n, i, a)
        } else i && (F(e, n, i, a), o[t] = void 0)
      }
    }
    const N = /(?:Once|Passive|Capture)$/;

    function A(e) {
      let t;
      if (N.test(e)) {
        let n;
        t = {};
        while (n = e.match(N)) e = e.slice(0, e.length - n[0].length), t[n[0].toLowerCase()] = !0
      }
      const n = ":" === e[2] ? e.slice(3) : (0, r.Tg)(e.slice(2));
      return [n, t]
    }
    let M = 0;
    const B = Promise.resolve(),
      j = () => M || (B.then((() => M = 0)), M = Date.now());

    function D(e, n) {
      const r = e => {
        if (e._vts) {
          if (e._vts <= r.attached) return
        } else e._vts = Date.now();
        (0, t.qL)(I(e, r.value), n, 5, [e])
      };
      return r.value = e, r.attached = j(), r
    }

    function I(e, t) {
      if ((0, r.cy)(t)) {
        const n = e.stopImmediatePropagation;
        return e.stopImmediatePropagation = () => {
          n.call(e), e._stopped = !0
        }, t.map((e => t => !t._stopped && e && e(t)))
      }
      return t
    }
    const U = e => 111 === e.charCodeAt(0) && 110 === e.charCodeAt(1) && e.charCodeAt(2) > 96 && e.charCodeAt(2) < 123,
      V = (e, t, n, s, o, i) => {
        const a = "svg" === o;
        "class" === t ? m(e, s, a) : "style" === t ? S(e, n, s) : (0, r.Mp)(t) ? (0, r.CP)(t) || P(e, t, n, s, i) : ("." === t[0] ? (t = t.slice(1), 1) : "^" === t[0] ? (t = t.slice(1), 0) : $(e, t, s, a)) ? (O(e, t, s), e.tagName.includes("-") || "value" !== t && "checked" !== t && "selected" !== t || C(e, t, s, a, i, "value" !== t)) : !e._isVueCE || !/[A-Z]/.test(t) && (0, r.Kg)(s) ? ("true-value" === t ? e._trueValue = s : "false-value" === t && (e._falseValue = s), C(e, t, s, a)) : O(e, (0, r.PT)(t), s, i, t)
      };

    function $(e, t, n, s) {
      if (s) return "innerHTML" === t || "textContent" === t || !!(t in e && U(t) && (0, r.Tn)(n));
      if ("spellcheck" === t || "draggable" === t || "translate" === t) return !1;
      if ("form" === t) return !1;
      if ("list" === t && "INPUT" === e.tagName) return !1;
      if ("type" === t && "TEXTAREA" === e.tagName) return !1;
      if ("width" === t || "height" === t) {
        const t = e.tagName;
        if ("IMG" === t || "VIDEO" === t || "CANVAS" === t || "SOURCE" === t) return !1
      }
      return (!U(t) || !(0, r.Kg)(n)) && t in e
    }
    /*! #__NO_SIDE_EFFECTS__ */
    "undefined" !== typeof HTMLElement && HTMLElement;
    Symbol("_moveCb"), Symbol("_enterCb");
    const W = e => {
      const t = e.props["onUpdate:modelValue"] || !1;
      return (0, r.cy)(t) ? e => (0, r.DY)(t, e) : t
    };

    function q(e) {
      e.target.composing = !0
    }

    function X(e) {
      const t = e.target;
      t.composing && (t.composing = !1, t.dispatchEvent(new Event("input")))
    }
    const K = Symbol("_assign"),
      H = {
        created(e, {
          modifiers: {
            lazy: t,
            trim: n,
            number: s
          }
        }, o) {
          e[K] = W(o);
          const i = s || o.props && "number" === o.props.type;
          R(e, t ? "change" : "input", (t => {
            if (t.target.composing) return;
            let s = e.value;
            n && (s = s.trim()), i && (s = (0, r.bB)(s)), e[K](s)
          })), n && R(e, "change", (() => {
            e.value = e.value.trim()
          })), t || (R(e, "compositionstart", q), R(e, "compositionend", X), R(e, "change", X))
        },
        mounted(e, {
          value: t
        }) {
          e.value = null == t ? "" : t
        },
        beforeUpdate(e, {
          value: t,
          oldValue: n,
          modifiers: {
            lazy: s,
            trim: o,
            number: i
          }
        }, a) {
          if (e[K] = W(a), e.composing) return;
          const l = !i && "number" !== e.type || /^0\d/.test(e.value) ? e.value : (0, r.bB)(e.value),
            c = null == t ? "" : t;
          if (l !== c) {
            if (document.activeElement === e && "range" !== e.type) {
              if (s && t === n) return;
              if (o && e.value.trim() === c) return
            }
            e.value = c
          }
        }
      },
      z = {
        deep: !0,
        created(e, t, n) {
          e[K] = W(n), R(e, "change", (() => {
            const t = e._modelValue,
              n = J(e),
              s = e.checked,
              o = e[K];
            if ((0, r.cy)(t)) {
              const e = (0, r.u3)(t, n),
                i = -1 !== e;
              if (s && !i) o(t.concat(n));
              else if (!s && i) {
                const n = [...t];
                n.splice(e, 1), o(n)
              }
            } else if ((0, r.vM)(t)) {
              const e = new Set(t);
              s ? e.add(n) : e.delete(n), o(e)
            } else o(Z(e, s))
          }))
        },
        mounted: G,
        beforeUpdate(e, t, n) {
          e[K] = W(n), G(e, t, n)
        }
      };

    function G(e, {
      value: t,
      oldValue: n
    }, s) {
      let o;
      if (e._modelValue = t, (0, r.cy)(t)) o = (0, r.u3)(t, s.props.value) > -1;
      else if ((0, r.vM)(t)) o = t.has(s.props.value);
      else {
        if (t === n) return;
        o = (0, r.BX)(t, Z(e, !0))
      }
      e.checked !== o && (e.checked = o)
    }
    const Q = {
      created(e, {
        value: t
      }, n) {
        e.checked = (0, r.BX)(t, n.props.value), e[K] = W(n), R(e, "change", (() => {
          e[K](J(e))
        }))
      },
      beforeUpdate(e, {
        value: t,
        oldValue: n
      }, s) {
        e[K] = W(s), t !== n && (e.checked = (0, r.BX)(t, s.props.value))
      }
    };

    function J(e) {
      return "_value" in e ? e._value : e.value
    }

    function Z(e, t) {
      const n = t ? "_trueValue" : "_falseValue";
      return n in e ? e[n] : t
    }
    const Y = ["ctrl", "shift", "alt", "meta"],
      ee = {
        stop: e => e.stopPropagation(),
        prevent: e => e.preventDefault(),
        self: e => e.target !== e.currentTarget,
        ctrl: e => !e.ctrlKey,
        shift: e => !e.shiftKey,
        alt: e => !e.altKey,
        meta: e => !e.metaKey,
        left: e => "button" in e && 0 !== e.button,
        middle: e => "button" in e && 1 !== e.button,
        right: e => "button" in e && 2 !== e.button,
        exact: (e, t) => Y.some((n => e[`${n}Key`] && !t.includes(n)))
      },
      te = (e, t) => {
        const n = e._withMods || (e._withMods = {}),
          r = t.join(".");
        return n[r] || (n[r] = (n, ...r) => {
          for (let e = 0; e < t.length; e++) {
            const r = ee[t[e]];
            if (r && r(n, t)) return
          }
          return e(n, ...r)
        })
      },
      ne = (0, r.X$)({
        patchProp: V
      }, d);
    let re;

    function se() {
      return re || (re = (0, t.K9)(ne))
    }
    const oe = (...e) => {
      const t = se().createApp(...e);
      const {
        mount: n
      } = t;
      return t.mount = e => {
        const s = ae(e);
        if (!s) return;
        const o = t._component;
        (0, r.Tn)(o) || o.render || o.template || (o.template = s.innerHTML), 1 === s.nodeType && (s.textContent = "");
        const i = n(s, !1, ie(s));
        return s instanceof Element && (s.removeAttribute("v-cloak"), s.setAttribute("data-v-app", "")), i
      }, t
    };

    function ie(e) {
      return e instanceof SVGElement ? "svg" : "function" === typeof MathMLElement && e instanceof MathMLElement ? "mathml" : void 0
    }

    function ae(e) {
      if ((0, r.Kg)(e)) {
        const t = document.querySelector(e);
        return t
      }
      return e
    }

    function le(e, n, r, s, o, i) {
      const a = (0, t.g2)("router-view");
      return (0, t.uX)(), (0, t.Wv)(a)
    }
    var ce = {
        name: "App"
      },
      ue = (n(7021), n(6262));
    const fe = (0, ue.A)(ce, [
      ["render", le]
    ]);
    var de = fe;
    /*!
     * vue-router v4.4.5
     * (c) 2024 Eduardo San Martin Morote
     * @license MIT
     */
    const he = "undefined" !== typeof document;

    function pe(e) {
      return "object" === typeof e || "displayName" in e || "props" in e || "__vccOpts" in e
    }

    function me(e) {
      return e.__esModule || "Module" === e[Symbol.toStringTag] || e.default && pe(e.default)
    }
    const ge = Object.assign;

    function ve(e, t) {
      const n = {};
      for (const r in t) {
        const s = t[r];
        n[r] = be(s) ? s.map(e) : e(s)
      }
      return n
    }
    const ye = () => {},
      be = Array.isArray;
    const Se = /#/g,
      we = /&/g,
      ke = /\//g,
      xe = /=/g,
      _e = /\?/g,
      Ee = /\+/g,
      Te = /%5B/g,
      Ce = /%5D/g,
      Oe = /%5E/g,
      Re = /%60/g,
      Fe = /%7B/g,
      Le = /%7C/g,
      Pe = /%7D/g,
      Ne = /%20/g;

    function Ae(e) {
      return encodeURI("" + e).replace(Le, "|").replace(Te, "[").replace(Ce, "]")
    }

    function Me(e) {
      return Ae(e).replace(Fe, "{").replace(Pe, "}").replace(Oe, "^")
    }

    function Be(e) {
      return Ae(e).replace(Ee, "%2B").replace(Ne, "+").replace(Se, "%23").replace(we, "%26").replace(Re, "`").replace(Fe, "{").replace(Pe, "}").replace(Oe, "^")
    }

    function je(e) {
      return Be(e).replace(xe, "%3D")
    }

    function De(e) {
      return Ae(e).replace(Se, "%23").replace(_e, "%3F")
    }

    function Ie(e) {
      return null == e ? "" : De(e).replace(ke, "%2F")
    }

    function Ue(e) {
      try {
        return decodeURIComponent("" + e)
      } catch (t) {}
      return "" + e
    }
    const Ve = /\/$/,
      $e = e => e.replace(Ve, "");

    function We(e, t, n = "/") {
      let r, s = {},
        o = "",
        i = "";
      const a = t.indexOf("#");
      let l = t.indexOf("?");
      return a < l && a >= 0 && (l = -1), l > -1 && (r = t.slice(0, l), o = t.slice(l + 1, a > -1 ? a : t.length), s = e(o)), a > -1 && (r = r || t.slice(0, a), i = t.slice(a, t.length)), r = Je(null != r ? r : t, n), {
        fullPath: r + (o && "?") + o + i,
        path: r,
        query: s,
        hash: Ue(i)
      }
    }

    function qe(e, t) {
      const n = t.query ? e(t.query) : "";
      return t.path + (n && "?") + n + (t.hash || "")
    }

    function Xe(e, t) {
      return t && e.toLowerCase().startsWith(t.toLowerCase()) ? e.slice(t.length) || "/" : e
    }

    function Ke(e, t, n) {
      const r = t.matched.length - 1,
        s = n.matched.length - 1;
      return r > -1 && r === s && He(t.matched[r], n.matched[s]) && ze(t.params, n.params) && e(t.query) === e(n.query) && t.hash === n.hash
    }

    function He(e, t) {
      return (e.aliasOf || e) === (t.aliasOf || t)
    }

    function ze(e, t) {
      if (Object.keys(e).length !== Object.keys(t).length) return !1;
      for (const n in e)
        if (!Ge(e[n], t[n])) return !1;
      return !0
    }

    function Ge(e, t) {
      return be(e) ? Qe(e, t) : be(t) ? Qe(t, e) : e === t
    }

    function Qe(e, t) {
      return be(t) ? e.length === t.length && e.every(((e, n) => e === t[n])) : 1 === e.length && e[0] === t
    }

    function Je(e, t) {
      if (e.startsWith("/")) return e;
      if (!e) return t;
      const n = t.split("/"),
        r = e.split("/"),
        s = r[r.length - 1];
      ".." !== s && "." !== s || r.push("");
      let o, i, a = n.length - 1;
      for (o = 0; o < r.length; o++)
        if (i = r[o], "." !== i) {
          if (".." !== i) break;
          a > 1 && a--
        } return n.slice(0, a).join("/") + "/" + r.slice(o).join("/")
    }
    const Ze = {
      path: "/",
      name: void 0,
      params: {},
      query: {},
      hash: "",
      fullPath: "/",
      matched: [],
      meta: {},
      redirectedFrom: void 0
    };
    var Ye, et;
    (function (e) {
      e["pop"] = "pop", e["push"] = "push"
    })(Ye || (Ye = {})),
    function (e) {
      e["back"] = "back", e["forward"] = "forward", e["unknown"] = ""
    }(et || (et = {}));

    function tt(e) {
      if (!e)
        if (he) {
          const t = document.querySelector("base");
          e = t && t.getAttribute("href") || "/", e = e.replace(/^\w+:\/\/[^\/]+/, "")
        } else e = "/";
      return "/" !== e[0] && "#" !== e[0] && (e = "/" + e), $e(e)
    }
    const nt = /^[^#]+#/;

    function rt(e, t) {
      return e.replace(nt, "#") + t
    }

    function st(e, t) {
      const n = document.documentElement.getBoundingClientRect(),
        r = e.getBoundingClientRect();
      return {
        behavior: t.behavior,
        left: r.left - n.left - (t.left || 0),
        top: r.top - n.top - (t.top || 0)
      }
    }
    const ot = () => ({
      left: window.scrollX,
      top: window.scrollY
    });

    function it(e) {
      let t;
      if ("el" in e) {
        const n = e.el,
          r = "string" === typeof n && n.startsWith("#");
        0;
        const s = "string" === typeof n ? r ? document.getElementById(n.slice(1)) : document.querySelector(n) : n;
        if (!s) return;
        t = st(s, e)
      } else t = e;
      "scrollBehavior" in document.documentElement.style ? window.scrollTo(t) : window.scrollTo(null != t.left ? t.left : window.scrollX, null != t.top ? t.top : window.scrollY)
    }

    function at(e, t) {
      const n = history.state ? history.state.position - t : -1;
      return n + e
    }
    const lt = new Map;

    function ct(e, t) {
      lt.set(e, t)
    }

    function ut(e) {
      const t = lt.get(e);
      return lt.delete(e), t
    }
    let ft = () => location.protocol + "//" + location.host;

    function dt(e, t) {
      const {
        pathname: n,
        search: r,
        hash: s
      } = t, o = e.indexOf("#");
      if (o > -1) {
        let t = s.includes(e.slice(o)) ? e.slice(o).length : 1,
          n = s.slice(t);
        return "/" !== n[0] && (n = "/" + n), Xe(n, "")
      }
      const i = Xe(n, e);
      return i + r + s
    }

    function ht(e, t, n, r) {
      let s = [],
        o = [],
        i = null;
      const a = ({
        state: o
      }) => {
        const a = dt(e, location),
          l = n.value,
          c = t.value;
        let u = 0;
        if (o) {
          if (n.value = a, t.value = o, i && i === l) return void(i = null);
          u = c ? o.position - c.position : 0
        } else r(a);
        s.forEach((e => {
          e(n.value, l, {
            delta: u,
            type: Ye.pop,
            direction: u ? u > 0 ? et.forward : et.back : et.unknown
          })
        }))
      };

      function l() {
        i = n.value
      }

      function c(e) {
        s.push(e);
        const t = () => {
          const t = s.indexOf(e);
          t > -1 && s.splice(t, 1)
        };
        return o.push(t), t
      }

      function u() {
        const {
          history: e
        } = window;
        e.state && e.replaceState(ge({}, e.state, {
          scroll: ot()
        }), "")
      }

      function f() {
        for (const e of o) e();
        o = [], window.removeEventListener("popstate", a), window.removeEventListener("beforeunload", u)
      }
      return window.addEventListener("popstate", a), window.addEventListener("beforeunload", u, {
        passive: !0
      }), {
        pauseListeners: l,
        listen: c,
        destroy: f
      }
    }

    function pt(e, t, n, r = !1, s = !1) {
      return {
        back: e,
        current: t,
        forward: n,
        replaced: r,
        position: window.history.length,
        scroll: s ? ot() : null
      }
    }

    function mt(e) {
      const {
        history: t,
        location: n
      } = window, r = {
        value: dt(e, n)
      }, s = {
        value: t.state
      };

      function o(r, o, i) {
        const a = e.indexOf("#"),
          l = a > -1 ? (n.host && document.querySelector("base") ? e : e.slice(a)) + r : ft() + e + r;
        try {
          t[i ? "replaceState" : "pushState"](o, "", l), s.value = o
        } catch (c) {
          console.error(c), n[i ? "replace" : "assign"](l)
        }
      }

      function i(e, n) {
        const i = ge({}, t.state, pt(s.value.back, e, s.value.forward, !0), n, {
          position: s.value.position
        });
        o(e, i, !0), r.value = e
      }

      function a(e, n) {
        const i = ge({}, s.value, t.state, {
          forward: e,
          scroll: ot()
        });
        o(i.current, i, !0);
        const a = ge({}, pt(r.value, e, null), {
          position: i.position + 1
        }, n);
        o(e, a, !1), r.value = e
      }
      return s.value || o(r.value, {
        back: null,
        current: r.value,
        forward: null,
        position: t.length - 1,
        replaced: !0,
        scroll: null
      }, !0), {
        location: r,
        state: s,
        push: a,
        replace: i
      }
    }

    function gt(e) {
      e = tt(e);
      const t = mt(e),
        n = ht(e, t.state, t.location, t.replace);

      function r(e, t = !0) {
        t || n.pauseListeners(), history.go(e)
      }
      const s = ge({
        location: "",
        base: e,
        go: r,
        createHref: rt.bind(null, e)
      }, t, n);
      return Object.defineProperty(s, "location", {
        enumerable: !0,
        get: () => t.location.value
      }), Object.defineProperty(s, "state", {
        enumerable: !0,
        get: () => t.state.value
      }), s
    }

    function vt(e) {
      return e = location.host ? e || location.pathname + location.search : "", e.includes("#") || (e += "#"), gt(e)
    }

    function yt(e) {
      return "string" === typeof e || e && "object" === typeof e
    }

    function bt(e) {
      return "string" === typeof e || "symbol" === typeof e
    }
    const St = Symbol("");
    var wt;
    (function (e) {
      e[e["aborted"] = 4] = "aborted", e[e["cancelled"] = 8] = "cancelled", e[e["duplicated"] = 16] = "duplicated"
    })(wt || (wt = {}));

    function kt(e, t) {
      return ge(new Error, {
        type: e,
        [St]: !0
      }, t)
    }

    function xt(e, t) {
      return e instanceof Error && St in e && (null == t || !!(e.type & t))
    }
    const _t = "[^/]+?",
      Et = {
        sensitive: !1,
        strict: !1,
        start: !0,
        end: !0
      },
      Tt = /[.+*?^${}()[\]/\\]/g;

    function Ct(e, t) {
      const n = ge({}, Et, t),
        r = [];
      let s = n.start ? "^" : "";
      const o = [];
      for (const u of e) {
        const e = u.length ? [] : [90];
        n.strict && !u.length && (s += "/");
        for (let t = 0; t < u.length; t++) {
          const r = u[t];
          let i = 40 + (n.sensitive ? .25 : 0);
          if (0 === r.type) t || (s += "/"), s += r.value.replace(Tt, "\\$&"), i += 40;
          else if (1 === r.type) {
            const {
              value: e,
              repeatable: n,
              optional: a,
              regexp: l
            } = r;
            o.push({
              name: e,
              repeatable: n,
              optional: a
            });
            const f = l || _t;
            if (f !== _t) {
              i += 10;
              try {
                new RegExp(`(${f})`)
              } catch (c) {
                throw new Error(`Invalid custom RegExp for param "${e}" (${f}): ` + c.message)
              }
            }
            let d = n ? `((?:${f})(?:/(?:${f}))*)` : `(${f})`;
            t || (d = a && u.length < 2 ? `(?:/${d})` : "/" + d), a && (d += "?"), s += d, i += 20, a && (i += -8), n && (i += -20), ".*" === f && (i += -50)
          }
          e.push(i)
        }
        r.push(e)
      }
      if (n.strict && n.end) {
        const e = r.length - 1;
        r[e][r[e].length - 1] += .7000000000000001
      }
      n.strict || (s += "/?"), n.end ? s += "$" : n.strict && (s += "(?:/|$)");
      const i = new RegExp(s, n.sensitive ? "" : "i");

      function a(e) {
        const t = e.match(i),
          n = {};
        if (!t) return null;
        for (let r = 1; r < t.length; r++) {
          const e = t[r] || "",
            s = o[r - 1];
          n[s.name] = e && s.repeatable ? e.split("/") : e
        }
        return n
      }

      function l(t) {
        let n = "",
          r = !1;
        for (const s of e) {
          r && n.endsWith("/") || (n += "/"), r = !1;
          for (const e of s)
            if (0 === e.type) n += e.value;
            else if (1 === e.type) {
            const {
              value: o,
              repeatable: i,
              optional: a
            } = e, l = o in t ? t[o] : "";
            if (be(l) && !i) throw new Error(`Provided param "${o}" is an array but it is not repeatable (* or + modifiers)`);
            const c = be(l) ? l.join("/") : l;
            if (!c) {
              if (!a) throw new Error(`Missing required param "${o}"`);
              s.length < 2 && (n.endsWith("/") ? n = n.slice(0, -1) : r = !0)
            }
            n += c
          }
        }
        return n || "/"
      }
      return {
        re: i,
        score: r,
        keys: o,
        parse: a,
        stringify: l
      }
    }

    function Ot(e, t) {
      let n = 0;
      while (n < e.length && n < t.length) {
        const r = t[n] - e[n];
        if (r) return r;
        n++
      }
      return e.length < t.length ? 1 === e.length && 80 === e[0] ? -1 : 1 : e.length > t.length ? 1 === t.length && 80 === t[0] ? 1 : -1 : 0
    }

    function Rt(e, t) {
      let n = 0;
      const r = e.score,
        s = t.score;
      while (n < r.length && n < s.length) {
        const e = Ot(r[n], s[n]);
        if (e) return e;
        n++
      }
      if (1 === Math.abs(s.length - r.length)) {
        if (Ft(r)) return 1;
        if (Ft(s)) return -1
      }
      return s.length - r.length
    }

    function Ft(e) {
      const t = e[e.length - 1];
      return e.length > 0 && t[t.length - 1] < 0
    }
    const Lt = {
        type: 0,
        value: ""
      },
      Pt = /[a-zA-Z0-9_]/;

    function Nt(e) {
      if (!e) return [
        []
      ];
      if ("/" === e) return [
        [Lt]
      ];
      if (!e.startsWith("/")) throw new Error(`Invalid path "${e}"`);

      function t(e) {
        throw new Error(`ERR (${n})/"${c}": ${e}`)
      }
      let n = 0,
        r = n;
      const s = [];
      let o;

      function i() {
        o && s.push(o), o = []
      }
      let a, l = 0,
        c = "",
        u = "";

      function f() {
        c && (0 === n ? o.push({
          type: 0,
          value: c
        }) : 1 === n || 2 === n || 3 === n ? (o.length > 1 && ("*" === a || "+" === a) && t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`), o.push({
          type: 1,
          value: c,
          regexp: u,
          repeatable: "*" === a || "+" === a,
          optional: "*" === a || "?" === a
        })) : t("Invalid state to consume buffer"), c = "")
      }

      function d() {
        c += a
      }
      while (l < e.length)
        if (a = e[l++], "\\" !== a || 2 === n) switch (n) {
          case 0:
            "/" === a ? (c && f(), i()) : ":" === a ? (f(), n = 1) : d();
            break;
          case 4:
            d(), n = r;
            break;
          case 1:
            "(" === a ? n = 2 : Pt.test(a) ? d() : (f(), n = 0, "*" !== a && "?" !== a && "+" !== a && l--);
            break;
          case 2:
            ")" === a ? "\\" == u[u.length - 1] ? u = u.slice(0, -1) + a : n = 3 : u += a;
            break;
          case 3:
            f(), n = 0, "*" !== a && "?" !== a && "+" !== a && l--, u = "";
            break;
          default:
            t("Unknown state");
            break
        } else r = n, n = 4;
      return 2 === n && t(`Unfinished custom RegExp for param "${c}"`), f(), i(), s
    }

    function At(e, t, n) {
      const r = Ct(Nt(e.path), n);
      const s = ge(r, {
        record: e,
        parent: t,
        children: [],
        alias: []
      });
      return t && !s.record.aliasOf === !t.record.aliasOf && t.children.push(s), s
    }

    function Mt(e, t) {
      const n = [],
        r = new Map;

      function s(e) {
        return r.get(e)
      }

      function o(e, n, r) {
        const s = !r,
          a = jt(e);
        a.aliasOf = r && r.record;
        const c = Vt(t, e),
          u = [a];
        if ("alias" in e) {
          const t = "string" === typeof e.alias ? [e.alias] : e.alias;
          for (const e of t) u.push(jt(ge({}, a, {
            components: r ? r.record.components : a.components,
            path: e,
            aliasOf: r ? r.record : a
          })))
        }
        let f, d;
        for (const t of u) {
          const {
            path: u
          } = t;
          if (n && "/" !== u[0]) {
            const e = n.record.path,
              r = "/" === e[e.length - 1] ? "" : "/";
            t.path = n.record.path + (u && r + u)
          }
          if (f = At(t, n, c), r ? r.alias.push(f) : (d = d || f, d !== f && d.alias.push(f), s && e.name && !It(f) && i(e.name)), qt(f) && l(f), a.children) {
            const e = a.children;
            for (let t = 0; t < e.length; t++) o(e[t], f, r && r.children[t])
          }
          r = r || f
        }
        return d ? () => {
          i(d)
        } : ye
      }

      function i(e) {
        if (bt(e)) {
          const t = r.get(e);
          t && (r.delete(e), n.splice(n.indexOf(t), 1), t.children.forEach(i), t.alias.forEach(i))
        } else {
          const t = n.indexOf(e);
          t > -1 && (n.splice(t, 1), e.record.name && r.delete(e.record.name), e.children.forEach(i), e.alias.forEach(i))
        }
      }

      function a() {
        return n
      }

      function l(e) {
        const t = $t(e, n);
        n.splice(t, 0, e), e.record.name && !It(e) && r.set(e.record.name, e)
      }

      function c(e, t) {
        let s, o, i, a = {};
        if ("name" in e && e.name) {
          if (s = r.get(e.name), !s) throw kt(1, {
            location: e
          });
          0, i = s.record.name, a = ge(Bt(t.params, s.keys.filter((e => !e.optional)).concat(s.parent ? s.parent.keys.filter((e => e.optional)) : []).map((e => e.name))), e.params && Bt(e.params, s.keys.map((e => e.name)))), o = s.stringify(a)
        } else if (null != e.path) o = e.path, s = n.find((e => e.re.test(o))), s && (a = s.parse(o), i = s.record.name);
        else {
          if (s = t.name ? r.get(t.name) : n.find((e => e.re.test(t.path))), !s) throw kt(1, {
            location: e,
            currentLocation: t
          });
          i = s.record.name, a = ge({}, t.params, e.params), o = s.stringify(a)
        }
        const l = [];
        let c = s;
        while (c) l.unshift(c.record), c = c.parent;
        return {
          name: i,
          path: o,
          params: a,
          matched: l,
          meta: Ut(l)
        }
      }

      function u() {
        n.length = 0, r.clear()
      }
      return t = Vt({
        strict: !1,
        end: !0,
        sensitive: !1
      }, t), e.forEach((e => o(e))), {
        addRoute: o,
        resolve: c,
        removeRoute: i,
        clearRoutes: u,
        getRoutes: a,
        getRecordMatcher: s
      }
    }

    function Bt(e, t) {
      const n = {};
      for (const r of t) r in e && (n[r] = e[r]);
      return n
    }

    function jt(e) {
      const t = {
        path: e.path,
        redirect: e.redirect,
        name: e.name,
        meta: e.meta || {},
        aliasOf: e.aliasOf,
        beforeEnter: e.beforeEnter,
        props: Dt(e),
        children: e.children || [],
        instances: {},
        leaveGuards: new Set,
        updateGuards: new Set,
        enterCallbacks: {},
        components: "components" in e ? e.components || null : e.component && {
          default: e.component
        }
      };
      return Object.defineProperty(t, "mods", {
        value: {}
      }), t
    }

    function Dt(e) {
      const t = {},
        n = e.props || !1;
      if ("component" in e) t.default = n;
      else
        for (const r in e.components) t[r] = "object" === typeof n ? n[r] : n;
      return t
    }

    function It(e) {
      while (e) {
        if (e.record.aliasOf) return !0;
        e = e.parent
      }
      return !1
    }

    function Ut(e) {
      return e.reduce(((e, t) => ge(e, t.meta)), {})
    }

    function Vt(e, t) {
      const n = {};
      for (const r in e) n[r] = r in t ? t[r] : e[r];
      return n
    }

    function $t(e, t) {
      let n = 0,
        r = t.length;
      while (n !== r) {
        const s = n + r >> 1,
          o = Rt(e, t[s]);
        o < 0 ? r = s : n = s + 1
      }
      const s = Wt(e);
      return s && (r = t.lastIndexOf(s, r - 1)), r
    }

    function Wt(e) {
      let t = e;
      while (t = t.parent)
        if (qt(t) && 0 === Rt(e, t)) return t
    }

    function qt({
      record: e
    }) {
      return !!(e.name || e.components && Object.keys(e.components).length || e.redirect)
    }

    function Xt(e) {
      const t = {};
      if ("" === e || "?" === e) return t;
      const n = "?" === e[0],
        r = (n ? e.slice(1) : e).split("&");
      for (let s = 0; s < r.length; ++s) {
        const e = r[s].replace(Ee, " "),
          n = e.indexOf("="),
          o = Ue(n < 0 ? e : e.slice(0, n)),
          i = n < 0 ? null : Ue(e.slice(n + 1));
        if (o in t) {
          let e = t[o];
          be(e) || (e = t[o] = [e]), e.push(i)
        } else t[o] = i
      }
      return t
    }

    function Kt(e) {
      let t = "";
      for (let n in e) {
        const r = e[n];
        if (n = je(n), null == r) {
          void 0 !== r && (t += (t.length ? "&" : "") + n);
          continue
        }
        const s = be(r) ? r.map((e => e && Be(e))) : [r && Be(r)];
        s.forEach((e => {
          void 0 !== e && (t += (t.length ? "&" : "") + n, null != e && (t += "=" + e))
        }))
      }
      return t
    }

    function Ht(e) {
      const t = {};
      for (const n in e) {
        const r = e[n];
        void 0 !== r && (t[n] = be(r) ? r.map((e => null == e ? null : "" + e)) : null == r ? r : "" + r)
      }
      return t
    }
    const zt = Symbol(""),
      Gt = Symbol(""),
      Qt = Symbol(""),
      Jt = Symbol(""),
      Zt = Symbol("");

    function Yt() {
      let e = [];

      function t(t) {
        return e.push(t), () => {
          const n = e.indexOf(t);
          n > -1 && e.splice(n, 1)
        }
      }

      function n() {
        e = []
      }
      return {
        add: t,
        list: () => e.slice(),
        reset: n
      }
    }

    function en(e, t, n, r, s, o = e => e()) {
      const i = r && (r.enterCallbacks[s] = r.enterCallbacks[s] || []);
      return () => new Promise(((a, l) => {
        const c = e => {
            !1 === e ? l(kt(4, {
              from: n,
              to: t
            })) : e instanceof Error ? l(e) : yt(e) ? l(kt(2, {
              from: t,
              to: e
            })) : (i && r.enterCallbacks[s] === i && "function" === typeof e && i.push(e), a())
          },
          u = o((() => e.call(r && r.instances[s], t, n, c)));
        let f = Promise.resolve(u);
        e.length < 3 && (f = f.then(c)), f.catch((e => l(e)))
      }))
    }

    function tn(e, t, n, r, s = e => e()) {
      const o = [];
      for (const i of e) {
        0;
        for (const e in i.components) {
          let a = i.components[e];
          if ("beforeRouteEnter" === t || i.instances[e])
            if (pe(a)) {
              const l = a.__vccOpts || a,
                c = l[t];
              c && o.push(en(c, n, r, i, e, s))
            } else {
              let l = a();
              0, o.push((() => l.then((o => {
                if (!o) throw new Error(`Couldn't resolve component "${e}" at "${i.path}"`);
                const a = me(o) ? o.default : o;
                i.mods[e] = o, i.components[e] = a;
                const l = a.__vccOpts || a,
                  c = l[t];
                return c && en(c, n, r, i, e, s)()
              }))))
            }
        }
      }
      return o
    }

    function nn(e) {
      const n = (0, t.WQ)(Qt),
        r = (0, t.WQ)(Jt);
      const o = (0, t.EW)((() => {
          const t = (0, s.R1)(e.to);
          return n.resolve(t)
        })),
        i = (0, t.EW)((() => {
          const {
            matched: e
          } = o.value, {
            length: t
          } = e, n = e[t - 1], s = r.matched;
          if (!n || !s.length) return -1;
          const i = s.findIndex(He.bind(null, n));
          if (i > -1) return i;
          const a = ln(e[t - 2]);
          return t > 1 && ln(n) === a && s[s.length - 1].path !== a ? s.findIndex(He.bind(null, e[t - 2])) : i
        })),
        a = (0, t.EW)((() => i.value > -1 && an(r.params, o.value.params))),
        l = (0, t.EW)((() => i.value > -1 && i.value === r.matched.length - 1 && ze(r.params, o.value.params)));

      function c(t = {}) {
        return on(t) ? n[(0, s.R1)(e.replace) ? "replace" : "push"]((0, s.R1)(e.to)).catch(ye) : Promise.resolve()
      }
      return {
        route: o,
        href: (0, t.EW)((() => o.value.href)),
        isActive: a,
        isExactActive: l,
        navigate: c
      }
    }
    const rn = (0, t.pM)({
        name: "RouterLink",
        compatConfig: {
          MODE: 3
        },
        props: {
          to: {
            type: [String, Object],
            required: !0
          },
          replace: Boolean,
          activeClass: String,
          exactActiveClass: String,
          custom: Boolean,
          ariaCurrentValue: {
            type: String,
            default: "page"
          }
        },
        useLink: nn,
        setup(e, {
          slots: n
        }) {
          const r = (0, s.Kh)(nn(e)),
            {
              options: o
            } = (0, t.WQ)(Qt),
            i = (0, t.EW)((() => ({
              [cn(e.activeClass, o.linkActiveClass, "router-link-active")]: r.isActive,
              [cn(e.exactActiveClass, o.linkExactActiveClass, "router-link-exact-active")]: r.isExactActive
            })));
          return () => {
            const s = n.default && n.default(r);
            return e.custom ? s : (0, t.h)("a", {
              "aria-current": r.isExactActive ? e.ariaCurrentValue : null,
              href: r.href,
              onClick: r.navigate,
              class: i.value
            }, s)
          }
        }
      }),
      sn = rn;

    function on(e) {
      if (!(e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) && !e.defaultPrevented && (void 0 === e.button || 0 === e.button)) {
        if (e.currentTarget && e.currentTarget.getAttribute) {
          const t = e.currentTarget.getAttribute("target");
          if (/\b_blank\b/i.test(t)) return
        }
        return e.preventDefault && e.preventDefault(), !0
      }
    }

    function an(e, t) {
      for (const n in t) {
        const r = t[n],
          s = e[n];
        if ("string" === typeof r) {
          if (r !== s) return !1
        } else if (!be(s) || s.length !== r.length || r.some(((e, t) => e !== s[t]))) return !1
      }
      return !0
    }

    function ln(e) {
      return e ? e.aliasOf ? e.aliasOf.path : e.path : ""
    }
    const cn = (e, t, n) => null != e ? e : null != t ? t : n,
      un = (0, t.pM)({
        name: "RouterView",
        inheritAttrs: !1,
        props: {
          name: {
            type: String,
            default: "default"
          },
          route: Object
        },
        compatConfig: {
          MODE: 3
        },
        setup(e, {
          attrs: n,
          slots: r
        }) {
          const o = (0, t.WQ)(Zt),
            i = (0, t.EW)((() => e.route || o.value)),
            a = (0, t.WQ)(Gt, 0),
            l = (0, t.EW)((() => {
              let e = (0, s.R1)(a);
              const {
                matched: t
              } = i.value;
              let n;
              while ((n = t[e]) && !n.components) e++;
              return e
            })),
            c = (0, t.EW)((() => i.value.matched[l.value]));
          (0, t.Gt)(Gt, (0, t.EW)((() => l.value + 1))), (0, t.Gt)(zt, c), (0, t.Gt)(Zt, i);
          const u = (0, s.KR)();
          return (0, t.wB)((() => [u.value, c.value, e.name]), (([e, t, n], [r, s, o]) => {
            t && (t.instances[n] = e, s && s !== t && e && e === r && (t.leaveGuards.size || (t.leaveGuards = s.leaveGuards), t.updateGuards.size || (t.updateGuards = s.updateGuards))), !e || !t || s && He(t, s) && r || (t.enterCallbacks[n] || []).forEach((t => t(e)))
          }), {
            flush: "post"
          }), () => {
            const s = i.value,
              o = e.name,
              a = c.value,
              l = a && a.components[o];
            if (!l) return fn(r.default, {
              Component: l,
              route: s
            });
            const f = a.props[o],
              d = f ? !0 === f ? s.params : "function" === typeof f ? f(s) : f : null,
              h = e => {
                e.component.isUnmounted && (a.instances[o] = null)
              },
              p = (0, t.h)(l, ge({}, d, n, {
                onVnodeUnmounted: h,
                ref: u
              }));
            return fn(r.default, {
              Component: p,
              route: s
            }) || p
          }
        }
      });

    function fn(e, t) {
      if (!e) return null;
      const n = e(t);
      return 1 === n.length ? n[0] : n
    }
    const dn = un;

    function hn(e) {
      const n = Mt(e.routes, e),
        r = e.parseQuery || Xt,
        o = e.stringifyQuery || Kt,
        i = e.history;
      const a = Yt(),
        l = Yt(),
        c = Yt(),
        u = (0, s.IJ)(Ze);
      let f = Ze;
      he && e.scrollBehavior && "scrollRestoration" in history && (history.scrollRestoration = "manual");
      const d = ve.bind(null, (e => "" + e)),
        h = ve.bind(null, Ie),
        p = ve.bind(null, Ue);

      function m(e, t) {
        let r, s;
        return bt(e) ? (r = n.getRecordMatcher(e), s = t) : s = e, n.addRoute(s, r)
      }

      function g(e) {
        const t = n.getRecordMatcher(e);
        t && n.removeRoute(t)
      }

      function v() {
        return n.getRoutes().map((e => e.record))
      }

      function y(e) {
        return !!n.getRecordMatcher(e)
      }

      function b(e, t) {
        if (t = ge({}, t || u.value), "string" === typeof e) {
          const s = We(r, e, t.path),
            o = n.resolve({
              path: s.path
            }, t),
            a = i.createHref(s.fullPath);
          return ge(s, o, {
            params: p(o.params),
            hash: Ue(s.hash),
            redirectedFrom: void 0,
            href: a
          })
        }
        let s;
        if (null != e.path) s = ge({}, e, {
          path: We(r, e.path, t.path).path
        });
        else {
          const n = ge({}, e.params);
          for (const e in n) null == n[e] && delete n[e];
          s = ge({}, e, {
            params: h(n)
          }), t.params = h(t.params)
        }
        const a = n.resolve(s, t),
          l = e.hash || "";
        a.params = d(p(a.params));
        const c = qe(o, ge({}, e, {
            hash: Me(l),
            path: a.path
          })),
          f = i.createHref(c);
        return ge({
          fullPath: c,
          hash: l,
          query: o === Kt ? Ht(e.query) : e.query || {}
        }, a, {
          redirectedFrom: void 0,
          href: f
        })
      }

      function S(e) {
        return "string" === typeof e ? We(r, e, u.value.path) : ge({}, e)
      }

      function w(e, t) {
        if (f !== e) return kt(8, {
          from: t,
          to: e
        })
      }

      function k(e) {
        return E(e)
      }

      function x(e) {
        return k(ge(S(e), {
          replace: !0
        }))
      }

      function _(e) {
        const t = e.matched[e.matched.length - 1];
        if (t && t.redirect) {
          const {
            redirect: n
          } = t;
          let r = "function" === typeof n ? n(e) : n;
          return "string" === typeof r && (r = r.includes("?") || r.includes("#") ? r = S(r) : {
            path: r
          }, r.params = {}), ge({
            query: e.query,
            hash: e.hash,
            params: null != r.path ? {} : e.params
          }, r)
        }
      }

      function E(e, t) {
        const n = f = b(e),
          r = u.value,
          s = e.state,
          i = e.force,
          a = !0 === e.replace,
          l = _(n);
        if (l) return E(ge(S(l), {
          state: "object" === typeof l ? ge({}, s, l.state) : s,
          force: i,
          replace: a
        }), t || n);
        const c = n;
        let d;
        return c.redirectedFrom = t, !i && Ke(o, r, n) && (d = kt(16, {
          to: c,
          from: r
        }), I(r, r, !0, !1)), (d ? Promise.resolve(d) : O(c, r)).catch((e => xt(e) ? xt(e, 2) ? e : D(e) : B(e, c, r))).then((e => {
          if (e) {
            if (xt(e, 2)) return E(ge({
              replace: a
            }, S(e.to), {
              state: "object" === typeof e.to ? ge({}, s, e.to.state) : s,
              force: i
            }), t || c)
          } else e = F(c, r, !0, a, s);
          return R(c, r, e), e
        }))
      }

      function T(e, t) {
        const n = w(e, t);
        return n ? Promise.reject(n) : Promise.resolve()
      }

      function C(e) {
        const t = $.values().next().value;
        return t && "function" === typeof t.runWithContext ? t.runWithContext(e) : e()
      }

      function O(e, t) {
        let n;
        const [r, s, o] = pn(e, t);
        n = tn(r.reverse(), "beforeRouteLeave", e, t);
        for (const a of r) a.leaveGuards.forEach((r => {
          n.push(en(r, e, t))
        }));
        const i = T.bind(null, e, t);
        return n.push(i), q(n).then((() => {
          n = [];
          for (const r of a.list()) n.push(en(r, e, t));
          return n.push(i), q(n)
        })).then((() => {
          n = tn(s, "beforeRouteUpdate", e, t);
          for (const r of s) r.updateGuards.forEach((r => {
            n.push(en(r, e, t))
          }));
          return n.push(i), q(n)
        })).then((() => {
          n = [];
          for (const r of o)
            if (r.beforeEnter)
              if (be(r.beforeEnter))
                for (const s of r.beforeEnter) n.push(en(s, e, t));
              else n.push(en(r.beforeEnter, e, t));
          return n.push(i), q(n)
        })).then((() => (e.matched.forEach((e => e.enterCallbacks = {})), n = tn(o, "beforeRouteEnter", e, t, C), n.push(i), q(n)))).then((() => {
          n = [];
          for (const r of l.list()) n.push(en(r, e, t));
          return n.push(i), q(n)
        })).catch((e => xt(e, 8) ? e : Promise.reject(e)))
      }

      function R(e, t, n) {
        c.list().forEach((r => C((() => r(e, t, n)))))
      }

      function F(e, t, n, r, s) {
        const o = w(e, t);
        if (o) return o;
        const a = t === Ze,
          l = he ? history.state : {};
        n && (r || a ? i.replace(e.fullPath, ge({
          scroll: a && l && l.scroll
        }, s)) : i.push(e.fullPath, s)), u.value = e, I(e, t, n, a), D()
      }
      let L;

      function P() {
        L || (L = i.listen(((e, t, n) => {
          if (!W.listening) return;
          const r = b(e),
            s = _(r);
          if (s) return void E(ge(s, {
            replace: !0
          }), r).catch(ye);
          f = r;
          const o = u.value;
          he && ct(at(o.fullPath, n.delta), ot()), O(r, o).catch((e => xt(e, 12) ? e : xt(e, 2) ? (E(e.to, r).then((e => {
            xt(e, 20) && !n.delta && n.type === Ye.pop && i.go(-1, !1)
          })).catch(ye), Promise.reject()) : (n.delta && i.go(-n.delta, !1), B(e, r, o)))).then((e => {
            e = e || F(r, o, !1), e && (n.delta && !xt(e, 8) ? i.go(-n.delta, !1) : n.type === Ye.pop && xt(e, 20) && i.go(-1, !1)), R(r, o, e)
          })).catch(ye)
        })))
      }
      let N, A = Yt(),
        M = Yt();

      function B(e, t, n) {
        D(e);
        const r = M.list();
        return r.length ? r.forEach((r => r(e, t, n))) : console.error(e), Promise.reject(e)
      }

      function j() {
        return N && u.value !== Ze ? Promise.resolve() : new Promise(((e, t) => {
          A.add([e, t])
        }))
      }

      function D(e) {
        return N || (N = !e, P(), A.list().forEach((([t, n]) => e ? n(e) : t())), A.reset()), e
      }

      function I(n, r, s, o) {
        const {
          scrollBehavior: i
        } = e;
        if (!he || !i) return Promise.resolve();
        const a = !s && ut(at(n.fullPath, 0)) || (o || !s) && history.state && history.state.scroll || null;
        return (0, t.dY)().then((() => i(n, r, a))).then((e => e && it(e))).catch((e => B(e, n, r)))
      }
      const U = e => i.go(e);
      let V;
      const $ = new Set,
        W = {
          currentRoute: u,
          listening: !0,
          addRoute: m,
          removeRoute: g,
          clearRoutes: n.clearRoutes,
          hasRoute: y,
          getRoutes: v,
          resolve: b,
          options: e,
          push: k,
          replace: x,
          go: U,
          back: () => U(-1),
          forward: () => U(1),
          beforeEach: a.add,
          beforeResolve: l.add,
          afterEach: c.add,
          onError: M.add,
          isReady: j,
          install(e) {
            const t = this;
            e.component("RouterLink", sn), e.component("RouterView", dn), e.config.globalProperties.$router = t, Object.defineProperty(e.config.globalProperties, "$route", {
              enumerable: !0,
              get: () => (0, s.R1)(u)
            }), he && !V && u.value === Ze && (V = !0, k(i.location).catch((e => {
              0
            })));
            const n = {};
            for (const s in Ze) Object.defineProperty(n, s, {
              get: () => u.value[s],
              enumerable: !0
            });
            e.provide(Qt, t), e.provide(Jt, (0, s.Gc)(n)), e.provide(Zt, u);
            const r = e.unmount;
            $.add(e), e.unmount = function () {
              $.delete(e), $.size < 1 && (f = Ze, L && L(), L = null, u.value = Ze, V = !1, N = !1), r()
            }
          }
        };

      function q(e) {
        return e.reduce(((e, t) => e.then((() => C(t)))), Promise.resolve())
      }
      return W
    }

    function pn(e, t) {
      const n = [],
        r = [],
        s = [],
        o = Math.max(t.matched.length, e.matched.length);
      for (let i = 0; i < o; i++) {
        const o = t.matched[i];
        o && (e.matched.find((e => He(e, o))) ? r.push(o) : n.push(o));
        const a = e.matched[i];
        a && (t.matched.find((e => He(e, a))) || s.push(a))
      }
      return [n, r, s]
    }

    function mn(e, n, r, s, o, i) {
      const a = (0, t.g2)("Nav"),
        l = (0, t.g2)("Main"),
        c = (0, t.g2)("Footer");
      return (0, t.uX)(), (0, t.CE)("div", null, [(0, t.bF)(a), (0, t.bF)(l), (0, t.bF)(c)])
    }
    const gn = {
        class: "modal-header"
      },
      vn = {
        class: "logo text-center"
      },
      yn = {
        key: 0,
        class: "mode"
      },
      bn = {
        key: 1,
        class: "mode"
      };

    function Sn(e, n, r, s, o, i) {
      return (0, t.uX)(), (0, t.CE)("div", gn, [(0, t.Lk)("h1", vn, [n[2] || (n[2] = (0, t.eW)(" TradingView Strategy Finder ")), n[3] || (n[3] = (0, t.Lk)("span", {
        class: "version"
      }, "(1.3.6) - ", -1)), "sequential" == e.searchMode ? ((0, t.uX)(), (0, t.CE)("span", yn, n[0] || (n[0] = [(0, t.Lk)("small", null, "Sequential Mode", -1)]))) : (0, t.Q3)("", !0), "smart" == e.searchMode ? ((0, t.uX)(), (0, t.CE)("span", bn, n[1] || (n[1] = [(0, t.Lk)("small", null, "Smart Mode", -1)]))) : (0, t.Q3)("", !0)])])
    }
    var wn = {
      name: "Nav",
      data: () => ({
        searchMode: "sequential"
      }),
      methods: {},
      created: function () {},
      mounted: function () {
        const e = localStorage.getItem("searchMode");
        null != e && (this.searchMode = e)
      }
    };
    n(5187);
    const kn = (0, ue.A)(wn, [
      ["render", Sn],
      ["__scopeId", "data-v-5128f18e"]
    ]);
    var xn = kn;
    n(4114);
    const _n = {
        class: "footer"
      },
      En = {
        class: "text-center"
      };

    function Tn(e, n, r, s, o, i) {
      return (0, t.uX)(), (0, t.CE)("footer", _n, [(0, t.Lk)("div", En, [n[2] || (n[2] = (0, t.Fv)('<span>TradingView Strategy Finder</span><br><small><a href="https://youtu.be/5LxXmqg81yg" target="_blank">How-to video</a></small> | <small>Support </small> <small><a href="https://discord.gg/3xqyXRm" target="_blank">@Discord</a></small> | ', 10)), (0, t.Lk)("small", null, [(0, t.Lk)("a", {
        href: "#",
        onClick: n[0] || (n[0] = te((t => e.$router.push("/settings")), ["prevent"]))
      }, n[1] || (n[1] = [(0, t.Lk)("i", {
        class: "fas fa-cog"
      }, null, -1), (0, t.eW)("Settings")]))])])])
    }
    var Cn = {
      name: "Footer",
      data: () => ({}),
      methods: {
        settings: function () {}
      },
      created: function () {}
    };
    const On = (0, ue.A)(Cn, [
      ["render", Tn]
    ]);
    var Rn = On;
    const Fn = {
        class: "modal-content",
        id: "content"
      },
      Ln = {
        key: 0
      },
      Pn = {
        class: "input-group input-group-sm mb-1"
      },
      Nn = ["onUpdate:modelValue"],
      An = ["onUpdate:modelValue"],
      Mn = {
        class: "input-group input-group-sm mb-1"
      },
      Bn = ["onUpdate:modelValue"],
      jn = ["onUpdate:modelValue"],
      Dn = {
        key: 0
      },
      In = {
        key: 0,
        class: "fa fa-search"
      },
      Un = {
        key: 1,
        id: "searchBtnText"
      },
      Vn = {
        key: 2,
        id: "searchBtnText"
      },
      $n = {
        key: 0,
        class: "spinner-border spinner-border-sm me-1",
        role: "status"
      },
      Wn = {
        key: 1,
        class: "fa fa-search"
      },
      qn = {
        class: "text-end"
      },
      Xn = {
        id: "avgTimespan"
      },
      Kn = {
        key: 0
      },
      Hn = {
        key: 0
      },
      zn = {
        key: 3,
        class: "alert alert-danger alert-msg",
        role: "alert"
      },
      Gn = {
        key: 4,
        class: "mt-1 alert alert-primary alert-msg",
        role: "alert"
      },
      Qn = {
        key: 5
      },
      Jn = {
        key: 0,
        class: "results"
      },
      Zn = {
        key: 0,
        style: {
          color: "red"
        }
      },
      Yn = {
        key: 1,
        class: "ml-2"
      },
      er = {
        key: 1,
        class: "results"
      },
      tr = {
        key: 0,
        style: {
          color: "red"
        }
      },
      nr = {
        key: 1,
        class: "ml-2"
      },
      rr = {
        key: 6
      },
      sr = {
        key: 0,
        class: "results"
      },
      or = ["title", "onClick"],
      ir = {
        key: 0,
        style: {
          color: "red"
        }
      },
      ar = {
        key: 1,
        class: "ml-2"
      },
      lr = ["onClick"],
      cr = {
        key: 1,
        class: "results"
      },
      ur = ["title", "onClick"],
      fr = {
        key: 0,
        style: {
          color: "red"
        }
      },
      dr = {
        key: 1,
        class: "ml-2"
      },
      hr = ["onClick"],
      pr = {
        style: {
          "font-size": "11px"
        },
        class: "mt-2 text-center"
      },
      mr = {
        key: 2
      },
      gr = {
        key: 1
      },
      vr = ["onClick"],
      yr = ["onClick"];

    function br(e, n, s, o, i, a) {
      return (0, t.uX)(), (0, t.CE)("div", null, [(0, t.Lk)("div", Fn, [e.showSavedSettings ? (0, t.Q3)("", !0) : ((0, t.uX)(), (0, t.CE)("div", Ln, [(0, t.Lk)("form", null, [(0, t.Lk)("a", {
        class: "float-end",
        href: "#",
        onClick: n[0] || (n[0] = e => a.saveSettings()),
        id: "save"
      }, n[26] || (n[26] = [(0, t.Lk)("i", {
        class: "fas fa-save"
      }, null, -1)])), (0, t.Lk)("a", {
        class: "float-end",
        href: "#",
        onClick: n[1] || (n[1] = e => a.loadSettings()),
        id: "open"
      }, n[27] || (n[27] = [(0, t.Lk)("i", {
        class: "fas fa-folder-open"
      }, null, -1)])), (0, t.bo)((0, t.Lk)("input", {
        class: "form-control form-control-sm mb-1",
        id: "pairs",
        type: "text",
        placeholder: "Pairs (e.g. BTCUSD, ETHUSD)",
        "onUpdate:modelValue": n[2] || (n[2] = t => e.pair = t),
        onKeyup: n[3] || (n[3] = e => a.calculateSteps())
      }, null, 544), [
        [H, e.pair]
      ]), (0, t.bo)((0, t.Lk)("input", {
        class: "form-control form-control-sm mb-1",
        id: "timeframes",
        type: "text",
        placeholder: "Timeframes (e.g. 30m, 1h, D)",
        "onUpdate:modelValue": n[4] || (n[4] = t => e.timeframe = t),
        onKeyup: n[5] || (n[5] = e => a.calculateSteps())
      }, null, 544), [
        [H, e.timeframe]
      ]), "smart" == e.searchMode ? (0, t.bo)(((0, t.uX)(), (0, t.CE)("input", {
        key: 0,
        class: "form-control form-control-sm mb-1",
        id: "searchRounds",
        type: "text",
        placeholder: "Search-Rounds (default: 1)",
        "onUpdate:modelValue": n[6] || (n[6] = t => e.searchRounds = t),
        onKeyup: n[7] || (n[7] = e => a.calculateSteps())
      }, null, 544)), [
        [H, e.searchRounds]
      ]) : (0, t.Q3)("", !0), ((0, t.uX)(!0), (0, t.CE)(t.FK, null, (0, t.pI)(e.fields, (e => ((0, t.uX)(), (0, t.CE)("div", {
        key: e.idx
      }, [(0, t.Lk)("div", Pn, [n[28] || (n[28] = (0, t.Lk)("div", {
        class: "input-group-prepend"
      }, [(0, t.Lk)("span", {
        class: "input-group-text",
        id: "inputGroup-sizing-sm"
      }, "Field")], -1)), (0, t.bo)((0, t.Lk)("input", {
        type: "text",
        class: "form-control",
        "aria-label": "Small",
        "aria-describedby": "inputGroup-sizing-sm",
        "onUpdate:modelValue": t => e.fieldNo = t,
        onKeyup: n[8] || (n[8] = e => a.saveCurrentState())
      }, null, 40, Nn), [
        [H, e.fieldNo]
      ]), n[29] || (n[29] = (0, t.Lk)("div", {
        class: "input-group-prepend"
      }, [(0, t.Lk)("span", {
        class: "input-group-text",
        id: "inputGroup-sizing-sm"
      }, "Step-Size")], -1)), (0, t.bo)((0, t.Lk)("input", {
        type: "text",
        class: "form-control",
        "aria-label": "Small",
        "aria-describedby": "inputGroup-sizing-sm",
        "onUpdate:modelValue": t => e.stepSize = t,
        onKeyup: n[9] || (n[9] = e => a.calculateSteps())
      }, null, 40, An), [
        [H, e.stepSize]
      ])]), (0, t.Lk)("div", Mn, [n[30] || (n[30] = (0, t.Lk)("div", {
        class: "input-group-prepend"
      }, [(0, t.Lk)("span", {
        class: "input-group-text",
        id: "inputGroup-sizing-sm"
      }, "Between")], -1)), (0, t.bo)((0, t.Lk)("input", {
        type: "text",
        class: "form-control",
        "aria-label": "Small",
        "aria-describedby": "inputGroup-sizing-sm",
        "onUpdate:modelValue": t => e.from = t,
        onKeyup: n[10] || (n[10] = e => a.calculateSteps())
      }, null, 40, Bn), [
        [H, e.from]
      ]), n[31] || (n[31] = (0, t.Lk)("div", {
        class: "input-group-prepend"
      }, [(0, t.Lk)("span", {
        class: "input-group-text",
        id: "inputGroup-sizing-sm"
      }, "and")], -1)), (0, t.bo)((0, t.Lk)("input", {
        type: "text",
        class: "form-control",
        "aria-label": "Small",
        "aria-describedby": "inputGroup-sizing-sm",
        "onUpdate:modelValue": t => e.to = t,
        onKeyup: n[11] || (n[11] = e => a.calculateSteps())
      }, null, 40, jn), [
        [H, e.to]
      ])])])))), 128)), e.fields.length > 1 ? ((0, t.uX)(), (0, t.CE)("a", {
        key: 1,
        href: "#",
        id: "remove",
        onClick: n[12] || (n[12] = e => a.removeField())
      }, n[32] || (n[32] = [(0, t.Lk)("i", {
        class: "fas fa-trash-alt mr-2"
      }, null, -1)]))) : (0, t.Q3)("", !0), (0, t.Lk)("a", {
        href: "#",
        onClick: n[13] || (n[13] = e => a.addField())
      }, n[33] || (n[33] = [(0, t.Lk)("i", {
        class: "fas fa-plus"
      }, null, -1)]))]), null != e.resumeObject ? ((0, t.uX)(), (0, t.CE)("span", Dn, [(0, t.Lk)("a", {
        href: "#",
        onClick: n[14] || (n[14] = e => a.resumeState())
      }, "Resume?")])) : (0, t.Q3)("", !0), (0, t.CE)("button", {
        class: "btn btn-primary btn-search mt-2 mb-1",
        id: "searchBtn",
        onClick: n[15] || (n[15] = e => a.changePair())
      }, [e.isRunning ? (0, t.Q3)("", !0) : ((0, t.uX)(), (0, t.CE)("i", In)), n[34] || (n[34] = (0, t.eW)()), e.isRunning ? (0, t.Q3)("", !0) : ((0, t.uX)(), (0, t.CE)("span", Un, "Find best settings")), n[35] || (n[35] = (0, t.eW)()), e.isRunning ? ((0, t.uX)(), (0, t.CE)("span", Vn, "Cancel")) : (0, t.Q3)("", !0)]), (0, t.Lk)("div", qn, [(0, t.Lk)("small", null, [(0, t.Lk)("span", null, [(0, t.Lk)("span", null, (0, r.v_)(e.currentStep.toLocaleString()), 1), n[38] || (n[38] = (0, t.eW)(" / ")), (0, t.Lk)("span", null, (0, r.v_)(e.stepCount.toLocaleString()), 1), n[39] || (n[39] = (0, t.eW)(" steps | ")), (0, t.Lk)("span", null, (0, r.v_)(e.timespan), 1), n[40] || (n[40] = (0, t.eW)(" | ")), (0, t.Lk)("span", Xn, [(0, t.eW)((0, r.v_)(e.avgTimespan) + " ", 1), "" != e.avgTimespan ? ((0, t.uX)(), (0, t.CE)("span", Kn, "sec/step")) : (0, t.Q3)("", !0)])])]), "smart" == e.searchMode ? ((0, t.uX)(), (0, t.CE)("small", Hn, [n[42] || (n[42] = (0, t.Lk)("br", null, null, -1)), (0, t.Lk)("span", null, [(0, t.Lk)("span", null, "Round: " + (0, r.v_)(e.currentSmartRound), 1), n[41] || (n[41] = (0, t.eW)(" / ")), (0, t.Lk)("span", null, (0, r.v_)(null != e.searchRounds ? e.searchRounds : 1), 1)])])) : (0, t.Q3)("", !0)]), e.error ? ((0, t.uX)(), (0, t.CE)("div", zn, [(0, t.eW)((0, r.v_)(e.errorMsg) + " ", 1)])) : (0, t.Q3)("", !0), e.showEarlyStoppageHint ? ((0, t.uX)(), (0, t.CE)("div", Gn, [(0, t.Lk)("span", {
        "aria-hidden": "true",
        onClick: n[18] || (n[18] = t => e.showEarlyStoppageHint = !1)
      }, n[43] || (n[43] = [(0, t.Lk)("i", {
        class: "float-end ms-4 mb-2 fas fa-times",
        style: {
          cursor: "pointer"
        }
      }, null, -1)])), n[44] || (n[44] = (0, t.Lk)("p", null, "Eearly stoppage: no improvement with further rounds!", -1))])) : (0, t.Q3)("", !0), null != e.currentBestSettings ? ((0, t.uX)(), (0, t.CE)("div", Qn, [e.currentBestSettings.isNotPercentValue ? (0, t.Q3)("", !0) : ((0, t.uX)(), (0, t.CE)("span", Jn, [(0, t.Lk)("a", {
        href: "javascript:void(0);",
        onClick: n[19] || (n[19] = t => a.changeBacktestSettings(e.currentBestSettings.pair, e.currentBestSettings.tf, e.currentBestSettings.fields, e.s.formFields))
      }, (0, r.v_)(e.currentBestSettings.pair) + " - " + (0, r.v_)(e.currentBestSettings.tf), 1), (0, t.eW)(": " + (0, r.v_)(null != e.currentBestSettings.backtestValue ? e.currentBestSettings.backtestValue.toFixed(2) : e.currentBestSettings.backtestValue) + " % ", 1), e.showDrawDown && e.currentBestSettings.drawDown ? ((0, t.uX)(), (0, t.CE)("span", Zn, "  - " + (0, r.v_)(null != e.currentBestSettings.drawDown ? e.currentBestSettings.drawDown.toFixed(2) : e.currentBestSettings.drawDown) + " % ", 1)) : (0, t.Q3)("", !0), n[45] || (n[45] = (0, t.eW)()), e.showNumOfTrades && null != e.currentBestSettings.totalTrades ? ((0, t.uX)(), (0, t.CE)("span", Yn, " | " + (0, r.v_)(e.currentBestSettings.totalTrades), 1)) : (0, t.Q3)("", !0)])), e.currentBestSettings.isNotPercentValue ? ((0, t.uX)(), (0, t.CE)("span", er, [(0, t.Lk)("a", {
        href: "javascript:void(0);",
        onClick: n[20] || (n[20] = t => a.changeBacktestSettings(e.currentBestSettings.pair, e.currentBestSettings.tf, e.currentBestSettings.fields, e.s.formFields))
      }, (0, r.v_)(e.currentBestSettings.pair) + " - " + (0, r.v_)(e.currentBestSettings.tf), 1), (0, t.eW)(": " + (0, r.v_)(null != e.currentBestSettings.backtestValue ? e.currentBestSettings.backtestValue.toFixed(3) : e.currentBestSettings.backtestValue) + " ", 1), e.showDrawDown && e.currentBestSettings.drawDown ? ((0, t.uX)(), (0, t.CE)("span", tr, "  - " + (0, r.v_)(null != e.currentBestSettings.drawDown ? e.currentBestSettings.drawDown.toFixed(2) : e.currentBestSettings.drawDown) + " % ", 1)) : (0, t.Q3)("", !0), n[46] || (n[46] = (0, t.eW)()), e.showNumOfTrades && null != e.currentBestSettings.totalTrades ? ((0, t.uX)(), (0, t.CE)("span", nr, " | " + (0, r.v_)(e.currentBestSettings.totalTrades), 1)) : (0, t.Q3)("", !0)])) : (0, t.Q3)("", !0)])) : (0, t.Q3)("", !0), e.bestSettings.length > 0 ? ((0, t.uX)(), (0, t.CE)("ul", rr, [((0, t.uX)(!0), (0, t.CE)(t.FK, null, (0, t.pI)(e.bestSettings, ((s, o) => ((0, t.uX)(), (0, t.CE)("li", {
        key: o
      }, [s.isNotPercentValue ? (0, t.Q3)("", !0) : ((0, t.uX)(), (0, t.CE)("span", sr, [(0, t.Lk)("a", {
        href: "javascript:void(0);",
        title: new Date(s.timestamp).toLocaleString(),
        onClick: e => a.changeBacktestSettings(s.pair, s.tf, s.fields, s.formFields)
      }, (0, r.v_)(s.pair) + " - " + (0, r.v_)(s.tf), 9, or), n[48] || (n[48] = (0, t.eW)(": ")), (0, t.Lk)("span", null, (0, r.v_)(null != s.backtestValue ? s.backtestValue.toFixed(2) : s.backtestValue) + " %", 1), n[49] || (n[49] = (0, t.eW)()), e.showDrawDown && s.drawDown ? ((0, t.uX)(), (0, t.CE)("span", ir, "  -" + (0, r.v_)(null != s.drawDown ? s.drawDown.toFixed(2) : s.drawDown) + " % ", 1)) : (0, t.Q3)("", !0), n[50] || (n[50] = (0, t.eW)()), e.showNumOfTrades && null != s.totalTrades ? ((0, t.uX)(), (0, t.CE)("span", ar, " | " + (0, r.v_)(s.totalTrades), 1)) : (0, t.Q3)("", !0), n[51] || (n[51] = (0, t.eW)()), (0, t.Lk)("a", {
        href: "javascript:void(0);",
        onClick: e => a.removeBacktest(o)
      }, n[47] || (n[47] = [(0, t.Lk)("i", {
        class: "far fa-trash-alt ms-3"
      }, null, -1)]), 8, lr)])), s.isNotPercentValue ? ((0, t.uX)(), (0, t.CE)("span", cr, [(0, t.Lk)("a", {
        href: "javascript:void(0);",
        title: new Date(s.timestamp).toLocaleString(),
        onClick: e => a.changeBacktestSettings(s.pair, s.tf, s.fields, s.formFields)
      }, (0, r.v_)(s.pair) + " - " + (0, r.v_)(s.tf), 9, ur), n[53] || (n[53] = (0, t.eW)(": ")), (0, t.Lk)("span", null, (0, r.v_)(null != s.backtestValue ? s.backtestValue.toFixed(3) : s.backtestValue), 1), n[54] || (n[54] = (0, t.eW)()), e.showDrawDown && s.drawDown ? ((0, t.uX)(), (0, t.CE)("span", fr, "  -" + (0, r.v_)(null != s.drawDown ? s.drawDown.toFixed(2) : s.drawDown) + " % ", 1)) : (0, t.Q3)("", !0), n[55] || (n[55] = (0, t.eW)()), e.showNumOfTrades && null != s.totalTrades ? ((0, t.uX)(), (0, t.CE)("span", dr, " | " + (0, r.v_)(s.totalTrades), 1)) : (0, t.Q3)("", !0), n[56] || (n[56] = (0, t.eW)()), (0, t.Lk)("a", {
        href: "javascript:void(0);",
        onClick: e => a.removeBacktest(o)
      }, n[52] || (n[52] = [(0, t.Lk)("i", {
        class: "far fa-trash-alt ms-3"
      }, null, -1)]), 8, hr)])) : (0, t.Q3)("", !0)])))), 128))])) : (0, t.Q3)("", !0), (0, t.Lk)("div", pr, [e.bestSettings.length > 0 ? ((0, t.uX)(), (0, t.CE)("a", {
        key: 0,
        class: "text-center",
        href: "#",
        onClick: n[21] || (n[21] = e => a.removeAllBacktest())
      }, "Remove all entries")) : (0, t.Q3)("", !0), n[59] || (n[59] = (0, t.eW)(" | ")), e.bestSettings.length > 0 ? ((0, t.uX)(), (0, t.CE)("a", {
        key: 1,
        class: "text-center",
        href: "#",
        onClick: n[22] || (n[22] = e => a.sortBestSettings(!0))
      }, "Sort by best")) : (0, t.Q3)("", !0), n[60] || (n[60] = (0, t.eW)(" | ")), e.showDrawDown ? ((0, t.uX)(), (0, t.CE)("span", mr, [e.bestSettings.length > 0 ? ((0, t.uX)(), (0, t.CE)("a", {
        key: 0,
        class: "text-center",
        href: "#",
        onClick: n[23] || (n[23] = e => a.sortBestSettings(!1, !0))
      }, "Sort by DD")) : (0, t.Q3)("", !0), n[57] || (n[57] = (0, t.eW)(" |"))])) : (0, t.Q3)("", !0), e.bestSettings.length > 0 ? ((0, t.uX)(), (0, t.CE)("a", {
        key: 3,
        class: "text-center",
        href: "#",
        onClick: n[24] || (n[24] = e => a.downloadCsv())
      }, n[58] || (n[58] = [(0, t.Lk)("i", {
        class: "fas fa-file-csv"
      }, null, -1), (0, t.eW)(" Download")]))) : (0, t.Q3)("", !0)])])), e.showSavedSettings ? ((0, t.uX)(), (0, t.CE)("div", gr, [(0, t.Lk)("ul", null, [((0, t.uX)(!0), (0, t.CE)(t.FK, null, (0, t.pI)(e.savedSettings, ((e, s) => ((0, t.uX)(), (0, t.CE)("li", {
        key: s
      }, [(0, t.Lk)("a", {
        class: "loadSetting",
        href: "javascript:void(0);",
        onClick: t => a.load(e)
      }, (0, r.v_)(e), 9, vr), n[61] || (n[61] = (0, t.eW)()), n[62] || (n[62] = (0, t.Lk)("a", {
        href: "javascript:void(0);"
      }, null, -1)), n[63] || (n[63] = (0, t.eW)()), (0, t.Lk)("i", {
        class: "fas fa-trash-alt mr-2 remove",
        onClick: t => a.removeSetting(e)
      }, null, 8, yr)])))), 128))]), (0, t.Lk)("button", {
        id: "backBtn",
        class: "btn btn-primary btn-search mt-4 mb-1",
        onClick: n[25] || (n[25] = (...e) => a.closeLoadingWindows && a.closeLoadingWindows(...e))
      }, "Back")])) : (0, t.Q3)("", !0)])])
    }
    n(4603), n(7566), n(8721);

    function Sr(e, t) {
      return function () {
        return e.apply(t, arguments)
      }
    }
    const {
      toString: wr
    } = Object.prototype, {
      getPrototypeOf: kr
    } = Object, xr = (e => t => {
      const n = wr.call(t);
      return e[n] || (e[n] = n.slice(8, -1).toLowerCase())
    })(Object.create(null)), _r = e => (e = e.toLowerCase(), t => xr(t) === e), Er = e => t => typeof t === e, {
      isArray: Tr
    } = Array, Cr = Er("undefined");

    function Or(e) {
      return null !== e && !Cr(e) && null !== e.constructor && !Cr(e.constructor) && Pr(e.constructor.isBuffer) && e.constructor.isBuffer(e)
    }
    const Rr = _r("ArrayBuffer");

    function Fr(e) {
      let t;
      return t = "undefined" !== typeof ArrayBuffer && ArrayBuffer.isView ? ArrayBuffer.isView(e) : e && e.buffer && Rr(e.buffer), t
    }
    const Lr = Er("string"),
      Pr = Er("function"),
      Nr = Er("number"),
      Ar = e => null !== e && "object" === typeof e,
      Mr = e => !0 === e || !1 === e,
      Br = e => {
        if ("object" !== xr(e)) return !1;
        const t = kr(e);
        return (null === t || t === Object.prototype || null === Object.getPrototypeOf(t)) && !(Symbol.toStringTag in e) && !(Symbol.iterator in e)
      },
      jr = _r("Date"),
      Dr = _r("File"),
      Ir = _r("Blob"),
      Ur = _r("FileList"),
      Vr = e => Ar(e) && Pr(e.pipe),
      $r = e => {
        let t;
        return e && ("function" === typeof FormData && e instanceof FormData || Pr(e.append) && ("formdata" === (t = xr(e)) || "object" === t && Pr(e.toString) && "[object FormData]" === e.toString()))
      },
      Wr = _r("URLSearchParams"),
      [qr, Xr, Kr, Hr] = ["ReadableStream", "Request", "Response", "Headers"].map(_r),
      zr = e => e.trim ? e.trim() : e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, "");

    function Gr(e, t, {
      allOwnKeys: n = !1
    } = {}) {
      if (null === e || "undefined" === typeof e) return;
      let r, s;
      if ("object" !== typeof e && (e = [e]), Tr(e))
        for (r = 0, s = e.length; r < s; r++) t.call(null, e[r], r, e);
      else {
        const s = n ? Object.getOwnPropertyNames(e) : Object.keys(e),
          o = s.length;
        let i;
        for (r = 0; r < o; r++) i = s[r], t.call(null, e[i], i, e)
      }
    }

    function Qr(e, t) {
      t = t.toLowerCase();
      const n = Object.keys(e);
      let r, s = n.length;
      while (s-- > 0)
        if (r = n[s], t === r.toLowerCase()) return r;
      return null
    }
    const Jr = (() => "undefined" !== typeof globalThis ? globalThis : "undefined" !== typeof self ? self : "undefined" !== typeof window ? window : global)(),
      Zr = e => !Cr(e) && e !== Jr;

    function Yr() {
      const {
        caseless: e
      } = Zr(this) && this || {}, t = {}, n = (n, r) => {
        const s = e && Qr(t, r) || r;
        Br(t[s]) && Br(n) ? t[s] = Yr(t[s], n) : Br(n) ? t[s] = Yr({}, n) : Tr(n) ? t[s] = n.slice() : t[s] = n
      };
      for (let r = 0, s = arguments.length; r < s; r++) arguments[r] && Gr(arguments[r], n);
      return t
    }
    const es = (e, t, n, {
        allOwnKeys: r
      } = {}) => (Gr(t, ((t, r) => {
        n && Pr(t) ? e[r] = Sr(t, n) : e[r] = t
      }), {
        allOwnKeys: r
      }), e),
      ts = e => (65279 === e.charCodeAt(0) && (e = e.slice(1)), e),
      ns = (e, t, n, r) => {
        e.prototype = Object.create(t.prototype, r), e.prototype.constructor = e, Object.defineProperty(e, "super", {
          value: t.prototype
        }), n && Object.assign(e.prototype, n)
      },
      rs = (e, t, n, r) => {
        let s, o, i;
        const a = {};
        if (t = t || {}, null == e) return t;
        do {
          s = Object.getOwnPropertyNames(e), o = s.length;
          while (o-- > 0) i = s[o], r && !r(i, e, t) || a[i] || (t[i] = e[i], a[i] = !0);
          e = !1 !== n && kr(e)
        } while (e && (!n || n(e, t)) && e !== Object.prototype);
        return t
      },
      ss = (e, t, n) => {
        e = String(e), (void 0 === n || n > e.length) && (n = e.length), n -= t.length;
        const r = e.indexOf(t, n);
        return -1 !== r && r === n
      },
      os = e => {
        if (!e) return null;
        if (Tr(e)) return e;
        let t = e.length;
        if (!Nr(t)) return null;
        const n = new Array(t);
        while (t-- > 0) n[t] = e[t];
        return n
      },
      is = (e => t => e && t instanceof e)("undefined" !== typeof Uint8Array && kr(Uint8Array)),
      as = (e, t) => {
        const n = e && e[Symbol.iterator],
          r = n.call(e);
        let s;
        while ((s = r.next()) && !s.done) {
          const n = s.value;
          t.call(e, n[0], n[1])
        }
      },
      ls = (e, t) => {
        let n;
        const r = [];
        while (null !== (n = e.exec(t))) r.push(n);
        return r
      },
      cs = _r("HTMLFormElement"),
      us = e => e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g, (function (e, t, n) {
        return t.toUpperCase() + n
      })),
      fs = (({
        hasOwnProperty: e
      }) => (t, n) => e.call(t, n))(Object.prototype),
      ds = _r("RegExp"),
      hs = (e, t) => {
        const n = Object.getOwnPropertyDescriptors(e),
          r = {};
        Gr(n, ((n, s) => {
          let o;
          !1 !== (o = t(n, s, e)) && (r[s] = o || n)
        })), Object.defineProperties(e, r)
      },
      ps = e => {
        hs(e, ((t, n) => {
          if (Pr(e) && -1 !== ["arguments", "caller", "callee"].indexOf(n)) return !1;
          const r = e[n];
          Pr(r) && (t.enumerable = !1, "writable" in t ? t.writable = !1 : t.set || (t.set = () => {
            throw Error("Can not rewrite read-only method '" + n + "'")
          }))
        }))
      },
      ms = (e, t) => {
        const n = {},
          r = e => {
            e.forEach((e => {
              n[e] = !0
            }))
          };
        return Tr(e) ? r(e) : r(String(e).split(t)), n
      },
      gs = () => {},
      vs = (e, t) => null != e && Number.isFinite(e = +e) ? e : t,
      ys = "abcdefghijklmnopqrstuvwxyz",
      bs = "0123456789",
      Ss = {
        DIGIT: bs,
        ALPHA: ys,
        ALPHA_DIGIT: ys + ys.toUpperCase() + bs
      },
      ws = (e = 16, t = Ss.ALPHA_DIGIT) => {
        let n = "";
        const {
          length: r
        } = t;
        while (e--) n += t[Math.random() * r | 0];
        return n
      };

    function ks(e) {
      return !!(e && Pr(e.append) && "FormData" === e[Symbol.toStringTag] && e[Symbol.iterator])
    }
    const xs = e => {
        const t = new Array(10),
          n = (e, r) => {
            if (Ar(e)) {
              if (t.indexOf(e) >= 0) return;
              if (!("toJSON" in e)) {
                t[r] = e;
                const s = Tr(e) ? [] : {};
                return Gr(e, ((e, t) => {
                  const o = n(e, r + 1);
                  !Cr(o) && (s[t] = o)
                })), t[r] = void 0, s
              }
            }
            return e
          };
        return n(e, 0)
      },
      _s = _r("AsyncFunction"),
      Es = e => e && (Ar(e) || Pr(e)) && Pr(e.then) && Pr(e.catch),
      Ts = ((e, t) => e ? setImmediate : t ? ((e, t) => (Jr.addEventListener("message", (({
        source: n,
        data: r
      }) => {
        n === Jr && r === e && t.length && t.shift()()
      }), !1), n => {
        t.push(n), Jr.postMessage(e, "*")
      }))(`axios@${Math.random()}`, []) : e => setTimeout(e))("function" === typeof setImmediate, Pr(Jr.postMessage)),
      Cs = "undefined" !== typeof queueMicrotask ? queueMicrotask.bind(Jr) : "undefined" !== typeof process && process.nextTick || Ts;
    var Os = {
      isArray: Tr,
      isArrayBuffer: Rr,
      isBuffer: Or,
      isFormData: $r,
      isArrayBufferView: Fr,
      isString: Lr,
      isNumber: Nr,
      isBoolean: Mr,
      isObject: Ar,
      isPlainObject: Br,
      isReadableStream: qr,
      isRequest: Xr,
      isResponse: Kr,
      isHeaders: Hr,
      isUndefined: Cr,
      isDate: jr,
      isFile: Dr,
      isBlob: Ir,
      isRegExp: ds,
      isFunction: Pr,
      isStream: Vr,
      isURLSearchParams: Wr,
      isTypedArray: is,
      isFileList: Ur,
      forEach: Gr,
      merge: Yr,
      extend: es,
      trim: zr,
      stripBOM: ts,
      inherits: ns,
      toFlatObject: rs,
      kindOf: xr,
      kindOfTest: _r,
      endsWith: ss,
      toArray: os,
      forEachEntry: as,
      matchAll: ls,
      isHTMLForm: cs,
      hasOwnProperty: fs,
      hasOwnProp: fs,
      reduceDescriptors: hs,
      freezeMethods: ps,
      toObjectSet: ms,
      toCamelCase: us,
      noop: gs,
      toFiniteNumber: vs,
      findKey: Qr,
      global: Jr,
      isContextDefined: Zr,
      ALPHABET: Ss,
      generateString: ws,
      isSpecCompliantForm: ks,
      toJSONObject: xs,
      isAsyncFn: _s,
      isThenable: Es,
      setImmediate: Ts,
      asap: Cs
    };

    function Rs(e, t, n, r, s) {
      Error.call(this), Error.captureStackTrace ? Error.captureStackTrace(this, this.constructor) : this.stack = (new Error).stack, this.message = e, this.name = "AxiosError", t && (this.code = t), n && (this.config = n), r && (this.request = r), s && (this.response = s, this.status = s.status ? s.status : null)
    }
    Os.inherits(Rs, Error, {
      toJSON: function () {
        return {
          message: this.message,
          name: this.name,
          description: this.description,
          number: this.number,
          fileName: this.fileName,
          lineNumber: this.lineNumber,
          columnNumber: this.columnNumber,
          stack: this.stack,
          config: Os.toJSONObject(this.config),
          code: this.code,
          status: this.status
        }
      }
    });
    const Fs = Rs.prototype,
      Ls = {};
    ["ERR_BAD_OPTION_VALUE", "ERR_BAD_OPTION", "ECONNABORTED", "ETIMEDOUT", "ERR_NETWORK", "ERR_FR_TOO_MANY_REDIRECTS", "ERR_DEPRECATED", "ERR_BAD_RESPONSE", "ERR_BAD_REQUEST", "ERR_CANCELED", "ERR_NOT_SUPPORT", "ERR_INVALID_URL"].forEach((e => {
      Ls[e] = {
        value: e
      }
    })), Object.defineProperties(Rs, Ls), Object.defineProperty(Fs, "isAxiosError", {
      value: !0
    }), Rs.from = (e, t, n, r, s, o) => {
      const i = Object.create(Fs);
      return Os.toFlatObject(e, i, (function (e) {
        return e !== Error.prototype
      }), (e => "isAxiosError" !== e)), Rs.call(i, e.message, t, n, r, s), i.cause = e, i.name = e.name, o && Object.assign(i, o), i
    };
    var Ps = Rs,
      Ns = null;

    function As(e) {
      return Os.isPlainObject(e) || Os.isArray(e)
    }

    function Ms(e) {
      return Os.endsWith(e, "[]") ? e.slice(0, -2) : e
    }

    function Bs(e, t, n) {
      return e ? e.concat(t).map((function (e, t) {
        return e = Ms(e), !n && t ? "[" + e + "]" : e
      })).join(n ? "." : "") : t
    }

    function js(e) {
      return Os.isArray(e) && !e.some(As)
    }
    const Ds = Os.toFlatObject(Os, {}, null, (function (e) {
      return /^is[A-Z]/.test(e)
    }));

    function Is(e, t, n) {
      if (!Os.isObject(e)) throw new TypeError("target must be an object");
      t = t || new(Ns || FormData), n = Os.toFlatObject(n, {
        metaTokens: !0,
        dots: !1,
        indexes: !1
      }, !1, (function (e, t) {
        return !Os.isUndefined(t[e])
      }));
      const r = n.metaTokens,
        s = n.visitor || u,
        o = n.dots,
        i = n.indexes,
        a = n.Blob || "undefined" !== typeof Blob && Blob,
        l = a && Os.isSpecCompliantForm(t);
      if (!Os.isFunction(s)) throw new TypeError("visitor must be a function");

      function c(e) {
        if (null === e) return "";
        if (Os.isDate(e)) return e.toISOString();
        if (!l && Os.isBlob(e)) throw new Ps("Blob is not supported. Use a Buffer instead.");
        return Os.isArrayBuffer(e) || Os.isTypedArray(e) ? l && "function" === typeof Blob ? new Blob([e]) : Buffer.from(e) : e
      }

      function u(e, n, s) {
        let a = e;
        if (e && !s && "object" === typeof e)
          if (Os.endsWith(n, "{}")) n = r ? n : n.slice(0, -2), e = JSON.stringify(e);
          else if (Os.isArray(e) && js(e) || (Os.isFileList(e) || Os.endsWith(n, "[]")) && (a = Os.toArray(e))) return n = Ms(n), a.forEach((function (e, r) {
          !Os.isUndefined(e) && null !== e && t.append(!0 === i ? Bs([n], r, o) : null === i ? n : n + "[]", c(e))
        })), !1;
        return !!As(e) || (t.append(Bs(s, n, o), c(e)), !1)
      }
      const f = [],
        d = Object.assign(Ds, {
          defaultVisitor: u,
          convertValue: c,
          isVisitable: As
        });

      function h(e, n) {
        if (!Os.isUndefined(e)) {
          if (-1 !== f.indexOf(e)) throw Error("Circular reference detected in " + n.join("."));
          f.push(e), Os.forEach(e, (function (e, r) {
            const o = !(Os.isUndefined(e) || null === e) && s.call(t, e, Os.isString(r) ? r.trim() : r, n, d);
            !0 === o && h(e, n ? n.concat(r) : [r])
          })), f.pop()
        }
      }
      if (!Os.isObject(e)) throw new TypeError("data must be an object");
      return h(e), t
    }
    var Us = Is;

    function Vs(e) {
      const t = {
        "!": "%21",
        "'": "%27",
        "(": "%28",
        ")": "%29",
        "~": "%7E",
        "%20": "+",
        "%00": "\0"
      };
      return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g, (function (e) {
        return t[e]
      }))
    }

    function $s(e, t) {
      this._pairs = [], e && Us(e, this, t)
    }
    const Ws = $s.prototype;
    Ws.append = function (e, t) {
      this._pairs.push([e, t])
    }, Ws.toString = function (e) {
      const t = e ? function (t) {
        return e.call(this, t, Vs)
      } : Vs;
      return this._pairs.map((function (e) {
        return t(e[0]) + "=" + t(e[1])
      }), "").join("&")
    };
    var qs = $s;

    function Xs(e) {
      return encodeURIComponent(e).replace(/%3A/gi, ":").replace(/%24/g, "$").replace(/%2C/gi, ",").replace(/%20/g, "+").replace(/%5B/gi, "[").replace(/%5D/gi, "]")
    }

    function Ks(e, t, n) {
      if (!t) return e;
      const r = n && n.encode || Xs,
        s = n && n.serialize;
      let o;
      if (o = s ? s(t, n) : Os.isURLSearchParams(t) ? t.toString() : new qs(t, n).toString(r), o) {
        const t = e.indexOf("#"); - 1 !== t && (e = e.slice(0, t)), e += (-1 === e.indexOf("?") ? "?" : "&") + o
      }
      return e
    }
    class Hs {
      constructor() {
        this.handlers = []
      }
      use(e, t, n) {
        return this.handlers.push({
          fulfilled: e,
          rejected: t,
          synchronous: !!n && n.synchronous,
          runWhen: n ? n.runWhen : null
        }), this.handlers.length - 1
      }
      eject(e) {
        this.handlers[e] && (this.handlers[e] = null)
      }
      clear() {
        this.handlers && (this.handlers = [])
      }
      forEach(e) {
        Os.forEach(this.handlers, (function (t) {
          null !== t && e(t)
        }))
      }
    }
    var zs = Hs,
      Gs = {
        silentJSONParsing: !0,
        forcedJSONParsing: !0,
        clarifyTimeoutError: !1
      },
      Qs = "undefined" !== typeof URLSearchParams ? URLSearchParams : qs,
      Js = "undefined" !== typeof FormData ? FormData : null,
      Zs = "undefined" !== typeof Blob ? Blob : null,
      Ys = {
        isBrowser: !0,
        classes: {
          URLSearchParams: Qs,
          FormData: Js,
          Blob: Zs
        },
        protocols: ["http", "https", "file", "blob", "url", "data"]
      };
    const eo = "undefined" !== typeof window && "undefined" !== typeof document,
      to = "object" === typeof navigator && navigator || void 0,
      no = eo && (!to || ["ReactNative", "NativeScript", "NS"].indexOf(to.product) < 0),
      ro = (() => "undefined" !== typeof WorkerGlobalScope && self instanceof WorkerGlobalScope && "function" === typeof self.importScripts)(),
      so = eo && window.location.href || "http://localhost";
    var oo = {
      ...e,
      ...Ys
    };

    function io(e, t) {
      return Us(e, new oo.classes.URLSearchParams, Object.assign({
        visitor: function (e, t, n, r) {
          return oo.isNode && Os.isBuffer(e) ? (this.append(t, e.toString("base64")), !1) : r.defaultVisitor.apply(this, arguments)
        }
      }, t))
    }

    function ao(e) {
      return Os.matchAll(/\w+|\[(\w*)]/g, e).map((e => "[]" === e[0] ? "" : e[1] || e[0]))
    }

    function lo(e) {
      const t = {},
        n = Object.keys(e);
      let r;
      const s = n.length;
      let o;
      for (r = 0; r < s; r++) o = n[r], t[o] = e[o];
      return t
    }

    function co(e) {
      function t(e, n, r, s) {
        let o = e[s++];
        if ("__proto__" === o) return !0;
        const i = Number.isFinite(+o),
          a = s >= e.length;
        if (o = !o && Os.isArray(r) ? r.length : o, a) return Os.hasOwnProp(r, o) ? r[o] = [r[o], n] : r[o] = n, !i;
        r[o] && Os.isObject(r[o]) || (r[o] = []);
        const l = t(e, n, r[o], s);
        return l && Os.isArray(r[o]) && (r[o] = lo(r[o])), !i
      }
      if (Os.isFormData(e) && Os.isFunction(e.entries)) {
        const n = {};
        return Os.forEachEntry(e, ((e, r) => {
          t(ao(e), r, n, 0)
        })), n
      }
      return null
    }
    var uo = co;

    function fo(e, t, n) {
      if (Os.isString(e)) try {
        return (t || JSON.parse)(e), Os.trim(e)
      } catch (ka) {
        if ("SyntaxError" !== ka.name) throw ka
      }
      return (n || JSON.stringify)(e)
    }
    const ho = {
      transitional: Gs,
      adapter: ["xhr", "http", "fetch"],
      transformRequest: [function (e, t) {
        const n = t.getContentType() || "",
          r = n.indexOf("application/json") > -1,
          s = Os.isObject(e);
        s && Os.isHTMLForm(e) && (e = new FormData(e));
        const o = Os.isFormData(e);
        if (o) return r ? JSON.stringify(uo(e)) : e;
        if (Os.isArrayBuffer(e) || Os.isBuffer(e) || Os.isStream(e) || Os.isFile(e) || Os.isBlob(e) || Os.isReadableStream(e)) return e;
        if (Os.isArrayBufferView(e)) return e.buffer;
        if (Os.isURLSearchParams(e)) return t.setContentType("application/x-www-form-urlencoded;charset=utf-8", !1), e.toString();
        let i;
        if (s) {
          if (n.indexOf("application/x-www-form-urlencoded") > -1) return io(e, this.formSerializer).toString();
          if ((i = Os.isFileList(e)) || n.indexOf("multipart/form-data") > -1) {
            const t = this.env && this.env.FormData;
            return Us(i ? {
              "files[]": e
            } : e, t && new t, this.formSerializer)
          }
        }
        return s || r ? (t.setContentType("application/json", !1), fo(e)) : e
      }],
      transformResponse: [function (e) {
        const t = this.transitional || ho.transitional,
          n = t && t.forcedJSONParsing,
          r = "json" === this.responseType;
        if (Os.isResponse(e) || Os.isReadableStream(e)) return e;
        if (e && Os.isString(e) && (n && !this.responseType || r)) {
          const n = t && t.silentJSONParsing,
            s = !n && r;
          try {
            return JSON.parse(e)
          } catch (ka) {
            if (s) {
              if ("SyntaxError" === ka.name) throw Ps.from(ka, Ps.ERR_BAD_RESPONSE, this, null, this.response);
              throw ka
            }
          }
        }
        return e
      }],
      timeout: 0,
      xsrfCookieName: "XSRF-TOKEN",
      xsrfHeaderName: "X-XSRF-TOKEN",
      maxContentLength: -1,
      maxBodyLength: -1,
      env: {
        FormData: oo.classes.FormData,
        Blob: oo.classes.Blob
      },
      validateStatus: function (e) {
        return e >= 200 && e < 300
      },
      headers: {
        common: {
          Accept: "application/json, text/plain, */*",
          "Content-Type": void 0
        }
      }
    };
    Os.forEach(["delete", "get", "head", "post", "put", "patch"], (e => {
      ho.headers[e] = {}
    }));
    var po = ho;
    const mo = Os.toObjectSet(["age", "authorization", "content-length", "content-type", "etag", "expires", "from", "host", "if-modified-since", "if-unmodified-since", "last-modified", "location", "max-forwards", "proxy-authorization", "referer", "retry-after", "user-agent"]);
    var go = e => {
      const t = {};
      let n, r, s;
      return e && e.split("\n").forEach((function (e) {
        s = e.indexOf(":"), n = e.substring(0, s).trim().toLowerCase(), r = e.substring(s + 1).trim(), !n || t[n] && mo[n] || ("set-cookie" === n ? t[n] ? t[n].push(r) : t[n] = [r] : t[n] = t[n] ? t[n] + ", " + r : r)
      })), t
    };
    const vo = Symbol("internals");

    function yo(e) {
      return e && String(e).trim().toLowerCase()
    }

    function bo(e) {
      return !1 === e || null == e ? e : Os.isArray(e) ? e.map(bo) : String(e)
    }

    function So(e) {
      const t = Object.create(null),
        n = /([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;
      let r;
      while (r = n.exec(e)) t[r[1]] = r[2];
      return t
    }
    const wo = e => /^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());

    function ko(e, t, n, r, s) {
      return Os.isFunction(r) ? r.call(this, t, n) : (s && (t = n), Os.isString(t) ? Os.isString(r) ? -1 !== t.indexOf(r) : Os.isRegExp(r) ? r.test(t) : void 0 : void 0)
    }

    function xo(e) {
      return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g, ((e, t, n) => t.toUpperCase() + n))
    }

    function _o(e, t) {
      const n = Os.toCamelCase(" " + t);
      ["get", "set", "has"].forEach((r => {
        Object.defineProperty(e, r + n, {
          value: function (e, n, s) {
            return this[r].call(this, t, e, n, s)
          },
          configurable: !0
        })
      }))
    }
    class Eo {
      constructor(e) {
        e && this.set(e)
      }
      set(e, t, n) {
        const r = this;

        function s(e, t, n) {
          const s = yo(t);
          if (!s) throw new Error("header name must be a non-empty string");
          const o = Os.findKey(r, s);
          (!o || void 0 === r[o] || !0 === n || void 0 === n && !1 !== r[o]) && (r[o || t] = bo(e))
        }
        const o = (e, t) => Os.forEach(e, ((e, n) => s(e, n, t)));
        if (Os.isPlainObject(e) || e instanceof this.constructor) o(e, t);
        else if (Os.isString(e) && (e = e.trim()) && !wo(e)) o(go(e), t);
        else if (Os.isHeaders(e))
          for (const [i, a] of e.entries()) s(a, i, n);
        else null != e && s(t, e, n);
        return this
      }
      get(e, t) {
        if (e = yo(e), e) {
          const n = Os.findKey(this, e);
          if (n) {
            const e = this[n];
            if (!t) return e;
            if (!0 === t) return So(e);
            if (Os.isFunction(t)) return t.call(this, e, n);
            if (Os.isRegExp(t)) return t.exec(e);
            throw new TypeError("parser must be boolean|regexp|function")
          }
        }
      }
      has(e, t) {
        if (e = yo(e), e) {
          const n = Os.findKey(this, e);
          return !(!n || void 0 === this[n] || t && !ko(this, this[n], n, t))
        }
        return !1
      }
      delete(e, t) {
        const n = this;
        let r = !1;

        function s(e) {
          if (e = yo(e), e) {
            const s = Os.findKey(n, e);
            !s || t && !ko(n, n[s], s, t) || (delete n[s], r = !0)
          }
        }
        return Os.isArray(e) ? e.forEach(s) : s(e), r
      }
      clear(e) {
        const t = Object.keys(this);
        let n = t.length,
          r = !1;
        while (n--) {
          const s = t[n];
          e && !ko(this, this[s], s, e, !0) || (delete this[s], r = !0)
        }
        return r
      }
      normalize(e) {
        const t = this,
          n = {};
        return Os.forEach(this, ((r, s) => {
          const o = Os.findKey(n, s);
          if (o) return t[o] = bo(r), void delete t[s];
          const i = e ? xo(s) : String(s).trim();
          i !== s && delete t[s], t[i] = bo(r), n[i] = !0
        })), this
      }
      concat(...e) {
        return this.constructor.concat(this, ...e)
      }
      toJSON(e) {
        const t = Object.create(null);
        return Os.forEach(this, ((n, r) => {
          null != n && !1 !== n && (t[r] = e && Os.isArray(n) ? n.join(", ") : n)
        })), t
      } [Symbol.iterator]() {
        return Object.entries(this.toJSON())[Symbol.iterator]()
      }
      toString() {
        return Object.entries(this.toJSON()).map((([e, t]) => e + ": " + t)).join("\n")
      }
      get[Symbol.toStringTag]() {
        return "AxiosHeaders"
      }
      static from(e) {
        return e instanceof this ? e : new this(e)
      }
      static concat(e, ...t) {
        const n = new this(e);
        return t.forEach((e => n.set(e))), n
      }
      static accessor(e) {
        const t = this[vo] = this[vo] = {
            accessors: {}
          },
          n = t.accessors,
          r = this.prototype;

        function s(e) {
          const t = yo(e);
          n[t] || (_o(r, e), n[t] = !0)
        }
        return Os.isArray(e) ? e.forEach(s) : s(e), this
      }
    }
    Eo.accessor(["Content-Type", "Content-Length", "Accept", "Accept-Encoding", "User-Agent", "Authorization"]), Os.reduceDescriptors(Eo.prototype, (({
      value: e
    }, t) => {
      let n = t[0].toUpperCase() + t.slice(1);
      return {
        get: () => e,
        set(e) {
          this[n] = e
        }
      }
    })), Os.freezeMethods(Eo);
    var To = Eo;

    function Co(e, t) {
      const n = this || po,
        r = t || n,
        s = To.from(r.headers);
      let o = r.data;
      return Os.forEach(e, (function (e) {
        o = e.call(n, o, s.normalize(), t ? t.status : void 0)
      })), s.normalize(), o
    }

    function Oo(e) {
      return !(!e || !e.__CANCEL__)
    }

    function Ro(e, t, n) {
      Ps.call(this, null == e ? "canceled" : e, Ps.ERR_CANCELED, t, n), this.name = "CanceledError"
    }
    Os.inherits(Ro, Ps, {
      __CANCEL__: !0
    });
    var Fo = Ro;

    function Lo(e, t, n) {
      const r = n.config.validateStatus;
      n.status && r && !r(n.status) ? t(new Ps("Request failed with status code " + n.status, [Ps.ERR_BAD_REQUEST, Ps.ERR_BAD_RESPONSE][Math.floor(n.status / 100) - 4], n.config, n.request, n)) : e(n)
    }

    function Po(e) {
      const t = /^([-+\w]{1,25})(:?\/\/|:)/.exec(e);
      return t && t[1] || ""
    }

    function No(e, t) {
      e = e || 10;
      const n = new Array(e),
        r = new Array(e);
      let s, o = 0,
        i = 0;
      return t = void 0 !== t ? t : 1e3,
        function (a) {
          const l = Date.now(),
            c = r[i];
          s || (s = l), n[o] = a, r[o] = l;
          let u = i,
            f = 0;
          while (u !== o) f += n[u++], u %= e;
          if (o = (o + 1) % e, o === i && (i = (i + 1) % e), l - s < t) return;
          const d = c && l - c;
          return d ? Math.round(1e3 * f / d) : void 0
        }
    }
    var Ao = No;

    function Mo(e, t) {
      let n, r, s = 0,
        o = 1e3 / t;
      const i = (t, o = Date.now()) => {
          s = o, n = null, r && (clearTimeout(r), r = null), e.apply(null, t)
        },
        a = (...e) => {
          const t = Date.now(),
            a = t - s;
          a >= o ? i(e, t) : (n = e, r || (r = setTimeout((() => {
            r = null, i(n)
          }), o - a)))
        },
        l = () => n && i(n);
      return [a, l]
    }
    var Bo = Mo;
    const jo = (e, t, n = 3) => {
        let r = 0;
        const s = Ao(50, 250);
        return Bo((n => {
          const o = n.loaded,
            i = n.lengthComputable ? n.total : void 0,
            a = o - r,
            l = s(a),
            c = o <= i;
          r = o;
          const u = {
            loaded: o,
            total: i,
            progress: i ? o / i : void 0,
            bytes: a,
            rate: l || void 0,
            estimated: l && i && c ? (i - o) / l : void 0,
            event: n,
            lengthComputable: null != i,
            [t ? "download" : "upload"]: !0
          };
          e(u)
        }), n)
      },
      Do = (e, t) => {
        const n = null != e;
        return [r => t[0]({
          lengthComputable: n,
          total: e,
          loaded: r
        }), t[1]]
      },
      Io = e => (...t) => Os.asap((() => e(...t)));
    var Uo = oo.hasStandardBrowserEnv ? function () {
        const e = oo.navigator && /(msie|trident)/i.test(oo.navigator.userAgent),
          t = document.createElement("a");
        let n;

        function r(n) {
          let r = n;
          return e && (t.setAttribute("href", r), r = t.href), t.setAttribute("href", r), {
            href: t.href,
            protocol: t.protocol ? t.protocol.replace(/:$/, "") : "",
            host: t.host,
            search: t.search ? t.search.replace(/^\?/, "") : "",
            hash: t.hash ? t.hash.replace(/^#/, "") : "",
            hostname: t.hostname,
            port: t.port,
            pathname: "/" === t.pathname.charAt(0) ? t.pathname : "/" + t.pathname
          }
        }
        return n = r(window.location.href),
          function (e) {
            const t = Os.isString(e) ? r(e) : e;
            return t.protocol === n.protocol && t.host === n.host
          }
      }() : function () {
        return function () {
          return !0
        }
      }(),
      Vo = oo.hasStandardBrowserEnv ? {
        write(e, t, n, r, s, o) {
          const i = [e + "=" + encodeURIComponent(t)];
          Os.isNumber(n) && i.push("expires=" + new Date(n).toGMTString()), Os.isString(r) && i.push("path=" + r), Os.isString(s) && i.push("domain=" + s), !0 === o && i.push("secure"), document.cookie = i.join("; ")
        },
        read(e) {
          const t = document.cookie.match(new RegExp("(^|;\\s*)(" + e + ")=([^;]*)"));
          return t ? decodeURIComponent(t[3]) : null
        },
        remove(e) {
          this.write(e, "", Date.now() - 864e5)
        }
      } : {
        write() {},
        read() {
          return null
        },
        remove() {}
      };

    function $o(e) {
      return /^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)
    }

    function Wo(e, t) {
      return t ? e.replace(/\/?\/$/, "") + "/" + t.replace(/^\/+/, "") : e
    }

    function qo(e, t) {
      return e && !$o(t) ? Wo(e, t) : t
    }
    const Xo = e => e instanceof To ? {
      ...e
    } : e;

    function Ko(e, t) {
      t = t || {};
      const n = {};

      function r(e, t, n) {
        return Os.isPlainObject(e) && Os.isPlainObject(t) ? Os.merge.call({
          caseless: n
        }, e, t) : Os.isPlainObject(t) ? Os.merge({}, t) : Os.isArray(t) ? t.slice() : t
      }

      function s(e, t, n) {
        return Os.isUndefined(t) ? Os.isUndefined(e) ? void 0 : r(void 0, e, n) : r(e, t, n)
      }

      function o(e, t) {
        if (!Os.isUndefined(t)) return r(void 0, t)
      }

      function i(e, t) {
        return Os.isUndefined(t) ? Os.isUndefined(e) ? void 0 : r(void 0, e) : r(void 0, t)
      }

      function a(n, s, o) {
        return o in t ? r(n, s) : o in e ? r(void 0, n) : void 0
      }
      const l = {
        url: o,
        method: o,
        data: o,
        baseURL: i,
        transformRequest: i,
        transformResponse: i,
        paramsSerializer: i,
        timeout: i,
        timeoutMessage: i,
        withCredentials: i,
        withXSRFToken: i,
        adapter: i,
        responseType: i,
        xsrfCookieName: i,
        xsrfHeaderName: i,
        onUploadProgress: i,
        onDownloadProgress: i,
        decompress: i,
        maxContentLength: i,
        maxBodyLength: i,
        beforeRedirect: i,
        transport: i,
        httpAgent: i,
        httpsAgent: i,
        cancelToken: i,
        socketPath: i,
        responseEncoding: i,
        validateStatus: a,
        headers: (e, t) => s(Xo(e), Xo(t), !0)
      };
      return Os.forEach(Object.keys(Object.assign({}, e, t)), (function (r) {
        const o = l[r] || s,
          i = o(e[r], t[r], r);
        Os.isUndefined(i) && o !== a || (n[r] = i)
      })), n
    }
    var Ho = e => {
      const t = Ko({}, e);
      let n, {
        data: r,
        withXSRFToken: s,
        xsrfHeaderName: o,
        xsrfCookieName: i,
        headers: a,
        auth: l
      } = t;
      if (t.headers = a = To.from(a), t.url = Ks(qo(t.baseURL, t.url), e.params, e.paramsSerializer), l && a.set("Authorization", "Basic " + btoa((l.username || "") + ":" + (l.password ? unescape(encodeURIComponent(l.password)) : ""))), Os.isFormData(r))
        if (oo.hasStandardBrowserEnv || oo.hasStandardBrowserWebWorkerEnv) a.setContentType(void 0);
        else if (!1 !== (n = a.getContentType())) {
        const [e, ...t] = n ? n.split(";").map((e => e.trim())).filter(Boolean) : [];
        a.setContentType([e || "multipart/form-data", ...t].join("; "))
      }
      if (oo.hasStandardBrowserEnv && (s && Os.isFunction(s) && (s = s(t)), s || !1 !== s && Uo(t.url))) {
        const e = o && i && Vo.read(i);
        e && a.set(o, e)
      }
      return t
    };
    const zo = "undefined" !== typeof XMLHttpRequest;
    var Go = zo && function (e) {
      return new Promise((function (t, n) {
        const r = Ho(e);
        let s = r.data;
        const o = To.from(r.headers).normalize();
        let i, a, l, c, u, {
          responseType: f,
          onUploadProgress: d,
          onDownloadProgress: h
        } = r;

        function p() {
          c && c(), u && u(), r.cancelToken && r.cancelToken.unsubscribe(i), r.signal && r.signal.removeEventListener("abort", i)
        }
        let m = new XMLHttpRequest;

        function g() {
          if (!m) return;
          const r = To.from("getAllResponseHeaders" in m && m.getAllResponseHeaders()),
            s = f && "text" !== f && "json" !== f ? m.response : m.responseText,
            o = {
              data: s,
              status: m.status,
              statusText: m.statusText,
              headers: r,
              config: e,
              request: m
            };
          Lo((function (e) {
            t(e), p()
          }), (function (e) {
            n(e), p()
          }), o), m = null
        }
        m.open(r.method.toUpperCase(), r.url, !0), m.timeout = r.timeout, "onloadend" in m ? m.onloadend = g : m.onreadystatechange = function () {
          m && 4 === m.readyState && (0 !== m.status || m.responseURL && 0 === m.responseURL.indexOf("file:")) && setTimeout(g)
        }, m.onabort = function () {
          m && (n(new Ps("Request aborted", Ps.ECONNABORTED, e, m)), m = null)
        }, m.onerror = function () {
          n(new Ps("Network Error", Ps.ERR_NETWORK, e, m)), m = null
        }, m.ontimeout = function () {
          let t = r.timeout ? "timeout of " + r.timeout + "ms exceeded" : "timeout exceeded";
          const s = r.transitional || Gs;
          r.timeoutErrorMessage && (t = r.timeoutErrorMessage), n(new Ps(t, s.clarifyTimeoutError ? Ps.ETIMEDOUT : Ps.ECONNABORTED, e, m)), m = null
        }, void 0 === s && o.setContentType(null), "setRequestHeader" in m && Os.forEach(o.toJSON(), (function (e, t) {
          m.setRequestHeader(t, e)
        })), Os.isUndefined(r.withCredentials) || (m.withCredentials = !!r.withCredentials), f && "json" !== f && (m.responseType = r.responseType), h && ([l, u] = jo(h, !0), m.addEventListener("progress", l)), d && m.upload && ([a, c] = jo(d), m.upload.addEventListener("progress", a), m.upload.addEventListener("loadend", c)), (r.cancelToken || r.signal) && (i = t => {
          m && (n(!t || t.type ? new Fo(null, e, m) : t), m.abort(), m = null)
        }, r.cancelToken && r.cancelToken.subscribe(i), r.signal && (r.signal.aborted ? i() : r.signal.addEventListener("abort", i)));
        const v = Po(r.url);
        v && -1 === oo.protocols.indexOf(v) ? n(new Ps("Unsupported protocol " + v + ":", Ps.ERR_BAD_REQUEST, e)) : m.send(s || null)
      }))
    };
    const Qo = (e, t) => {
      const {
        length: n
      } = e = e ? e.filter(Boolean) : [];
      if (t || n) {
        let n, r = new AbortController;
        const s = function (e) {
          if (!n) {
            n = !0, i();
            const t = e instanceof Error ? e : this.reason;
            r.abort(t instanceof Ps ? t : new Fo(t instanceof Error ? t.message : t))
          }
        };
        let o = t && setTimeout((() => {
          o = null, s(new Ps(`timeout ${t} of ms exceeded`, Ps.ETIMEDOUT))
        }), t);
        const i = () => {
          e && (o && clearTimeout(o), o = null, e.forEach((e => {
            e.unsubscribe ? e.unsubscribe(s) : e.removeEventListener("abort", s)
          })), e = null)
        };
        e.forEach((e => e.addEventListener("abort", s)));
        const {
          signal: a
        } = r;
        return a.unsubscribe = () => Os.asap(i), a
      }
    };
    var Jo = Qo;
    const Zo = function* (e, t) {
        let n = e.byteLength;
        if (!t || n < t) return void(yield e);
        let r, s = 0;
        while (s < n) r = s + t, yield e.slice(s, r), s = r
      },
      Yo = async function* (e, t) {
        for await (const n of ei(e)) yield* Zo(n, t)
      }, ei = async function* (e) {
        if (e[Symbol.asyncIterator]) return void(yield* e);
        const t = e.getReader();
        try {
          for (;;) {
            const {
              done: e,
              value: n
            } = await t.read();
            if (e) break;
            yield n
          }
        } finally {
          await t.cancel()
        }
      }, ti = (e, t, n, r) => {
        const s = Yo(e, t);
        let o, i = 0,
          a = e => {
            o || (o = !0, r && r(e))
          };
        return new ReadableStream({
          async pull(e) {
            try {
              const {
                done: t,
                value: r
              } = await s.next();
              if (t) return a(), void e.close();
              let o = r.byteLength;
              if (n) {
                let e = i += o;
                n(e)
              }
              e.enqueue(new Uint8Array(r))
            } catch (t) {
              throw a(t), t
            }
          },
          cancel(e) {
            return a(e), s.return()
          }
        }, {
          highWaterMark: 2
        })
      }, ni = "function" === typeof fetch && "function" === typeof Request && "function" === typeof Response, ri = ni && "function" === typeof ReadableStream, si = ni && ("function" === typeof TextEncoder ? (e => t => e.encode(t))(new TextEncoder) : async e => new Uint8Array(await new Response(e).arrayBuffer())), oi = (e, ...t) => {
        try {
          return !!e(...t)
        } catch (ka) {
          return !1
        }
      }, ii = ri && oi((() => {
        let e = !1;
        const t = new Request(oo.origin, {
          body: new ReadableStream,
          method: "POST",
          get duplex() {
            return e = !0, "half"
          }
        }).headers.has("Content-Type");
        return e && !t
      })), ai = 65536, li = ri && oi((() => Os.isReadableStream(new Response("").body))), ci = {
        stream: li && (e => e.body)
      };
    ni && (e => {
      ["text", "arrayBuffer", "blob", "formData", "stream"].forEach((t => {
        !ci[t] && (ci[t] = Os.isFunction(e[t]) ? e => e[t]() : (e, n) => {
          throw new Ps(`Response type '${t}' is not supported`, Ps.ERR_NOT_SUPPORT, n)
        })
      }))
    })(new Response);
    const ui = async e => {
      if (null == e) return 0;
      if (Os.isBlob(e)) return e.size;
      if (Os.isSpecCompliantForm(e)) {
        const t = new Request(oo.origin, {
          method: "POST",
          body: e
        });
        return (await t.arrayBuffer()).byteLength
      }
      return Os.isArrayBufferView(e) || Os.isArrayBuffer(e) ? e.byteLength : (Os.isURLSearchParams(e) && (e += ""), Os.isString(e) ? (await si(e)).byteLength : void 0)
    }, fi = async (e, t) => {
      const n = Os.toFiniteNumber(e.getContentLength());
      return null == n ? ui(t) : n
    };
    var di = ni && (async e => {
      let {
        url: t,
        method: n,
        data: r,
        signal: s,
        cancelToken: o,
        timeout: i,
        onDownloadProgress: a,
        onUploadProgress: l,
        responseType: c,
        headers: u,
        withCredentials: f = "same-origin",
        fetchOptions: d
      } = Ho(e);
      c = c ? (c + "").toLowerCase() : "text";
      let h, p = Jo([s, o && o.toAbortSignal()], i);
      const m = p && p.unsubscribe && (() => {
        p.unsubscribe()
      });
      let g;
      try {
        if (l && ii && "get" !== n && "head" !== n && 0 !== (g = await fi(u, r))) {
          let e, n = new Request(t, {
            method: "POST",
            body: r,
            duplex: "half"
          });
          if (Os.isFormData(r) && (e = n.headers.get("content-type")) && u.setContentType(e), n.body) {
            const [e, t] = Do(g, jo(Io(l)));
            r = ti(n.body, ai, e, t)
          }
        }
        Os.isString(f) || (f = f ? "include" : "omit");
        const s = "credentials" in Request.prototype;
        h = new Request(t, {
          ...d,
          signal: p,
          method: n.toUpperCase(),
          headers: u.normalize().toJSON(),
          body: r,
          duplex: "half",
          credentials: s ? f : void 0
        });
        let o = await fetch(h);
        const i = li && ("stream" === c || "response" === c);
        if (li && (a || i && m)) {
          const e = {};
          ["status", "statusText", "headers"].forEach((t => {
            e[t] = o[t]
          }));
          const t = Os.toFiniteNumber(o.headers.get("content-length")),
            [n, r] = a && Do(t, jo(Io(a), !0)) || [];
          o = new Response(ti(o.body, ai, n, (() => {
            r && r(), m && m()
          })), e)
        }
        c = c || "text";
        let v = await ci[Os.findKey(ci, c) || "text"](o, e);
        return !i && m && m(), await new Promise(((t, n) => {
          Lo(t, n, {
            data: v,
            headers: To.from(o.headers),
            status: o.status,
            statusText: o.statusText,
            config: e,
            request: h
          })
        }))
      } catch (v) {
        if (m && m(), v && "TypeError" === v.name && /fetch/i.test(v.message)) throw Object.assign(new Ps("Network Error", Ps.ERR_NETWORK, e, h), {
          cause: v.cause || v
        });
        throw Ps.from(v, v && v.code, e, h)
      }
    });
    const hi = {
      http: Ns,
      xhr: Go,
      fetch: di
    };
    Os.forEach(hi, ((e, t) => {
      if (e) {
        try {
          Object.defineProperty(e, "name", {
            value: t
          })
        } catch (ka) {}
        Object.defineProperty(e, "adapterName", {
          value: t
        })
      }
    }));
    const pi = e => `- ${e}`,
      mi = e => Os.isFunction(e) || null === e || !1 === e;
    var gi = {
      getAdapter: e => {
        e = Os.isArray(e) ? e : [e];
        const {
          length: t
        } = e;
        let n, r;
        const s = {};
        for (let o = 0; o < t; o++) {
          let t;
          if (n = e[o], r = n, !mi(n) && (r = hi[(t = String(n)).toLowerCase()], void 0 === r)) throw new Ps(`Unknown adapter '${t}'`);
          if (r) break;
          s[t || "#" + o] = r
        }
        if (!r) {
          const e = Object.entries(s).map((([e, t]) => `adapter ${e} ` + (!1 === t ? "is not supported by the environment" : "is not available in the build")));
          let n = t ? e.length > 1 ? "since :\n" + e.map(pi).join("\n") : " " + pi(e[0]) : "as no adapter specified";
          throw new Ps("There is no suitable adapter to dispatch the request " + n, "ERR_NOT_SUPPORT")
        }
        return r
      },
      adapters: hi
    };

    function vi(e) {
      if (e.cancelToken && e.cancelToken.throwIfRequested(), e.signal && e.signal.aborted) throw new Fo(null, e)
    }

    function yi(e) {
      vi(e), e.headers = To.from(e.headers), e.data = Co.call(e, e.transformRequest), -1 !== ["post", "put", "patch"].indexOf(e.method) && e.headers.setContentType("application/x-www-form-urlencoded", !1);
      const t = gi.getAdapter(e.adapter || po.adapter);
      return t(e).then((function (t) {
        return vi(e), t.data = Co.call(e, e.transformResponse, t), t.headers = To.from(t.headers), t
      }), (function (t) {
        return Oo(t) || (vi(e), t && t.response && (t.response.data = Co.call(e, e.transformResponse, t.response), t.response.headers = To.from(t.response.headers))), Promise.reject(t)
      }))
    }
    const bi = "1.7.7",
      Si = {};
    ["object", "boolean", "number", "function", "string", "symbol"].forEach(((e, t) => {
      Si[e] = function (n) {
        return typeof n === e || "a" + (t < 1 ? "n " : " ") + e
      }
    }));
    const wi = {};

    function ki(e, t, n) {
      if ("object" !== typeof e) throw new Ps("options must be an object", Ps.ERR_BAD_OPTION_VALUE);
      const r = Object.keys(e);
      let s = r.length;
      while (s-- > 0) {
        const o = r[s],
          i = t[o];
        if (i) {
          const t = e[o],
            n = void 0 === t || i(t, o, e);
          if (!0 !== n) throw new Ps("option " + o + " must be " + n, Ps.ERR_BAD_OPTION_VALUE)
        } else if (!0 !== n) throw new Ps("Unknown option " + o, Ps.ERR_BAD_OPTION)
      }
    }
    Si.transitional = function (e, t, n) {
      function r(e, t) {
        return "[Axios v" + bi + "] Transitional option '" + e + "'" + t + (n ? ". " + n : "")
      }
      return (n, s, o) => {
        if (!1 === e) throw new Ps(r(s, " has been removed" + (t ? " in " + t : "")), Ps.ERR_DEPRECATED);
        return t && !wi[s] && (wi[s] = !0, console.warn(r(s, " has been deprecated since v" + t + " and will be removed in the near future"))), !e || e(n, s, o)
      }
    };
    var xi = {
      assertOptions: ki,
      validators: Si
    };
    const _i = xi.validators;
    class Ei {
      constructor(e) {
        this.defaults = e, this.interceptors = {
          request: new zs,
          response: new zs
        }
      }
      async request(e, t) {
        try {
          return await this._request(e, t)
        } catch (n) {
          if (n instanceof Error) {
            let e;
            Error.captureStackTrace ? Error.captureStackTrace(e = {}) : e = new Error;
            const t = e.stack ? e.stack.replace(/^.+\n/, "") : "";
            try {
              n.stack ? t && !String(n.stack).endsWith(t.replace(/^.+\n.+\n/, "")) && (n.stack += "\n" + t) : n.stack = t
            } catch (ka) {}
          }
          throw n
        }
      }
      _request(e, t) {
        "string" === typeof e ? (t = t || {}, t.url = e) : t = e || {}, t = Ko(this.defaults, t);
        const {
          transitional: n,
          paramsSerializer: r,
          headers: s
        } = t;
        void 0 !== n && xi.assertOptions(n, {
          silentJSONParsing: _i.transitional(_i.boolean),
          forcedJSONParsing: _i.transitional(_i.boolean),
          clarifyTimeoutError: _i.transitional(_i.boolean)
        }, !1), null != r && (Os.isFunction(r) ? t.paramsSerializer = {
          serialize: r
        } : xi.assertOptions(r, {
          encode: _i.function,
          serialize: _i.function
        }, !0)), t.method = (t.method || this.defaults.method || "get").toLowerCase();
        let o = s && Os.merge(s.common, s[t.method]);
        s && Os.forEach(["delete", "get", "head", "post", "put", "patch", "common"], (e => {
          delete s[e]
        })), t.headers = To.concat(o, s);
        const i = [];
        let a = !0;
        this.interceptors.request.forEach((function (e) {
          "function" === typeof e.runWhen && !1 === e.runWhen(t) || (a = a && e.synchronous, i.unshift(e.fulfilled, e.rejected))
        }));
        const l = [];
        let c;
        this.interceptors.response.forEach((function (e) {
          l.push(e.fulfilled, e.rejected)
        }));
        let u, f = 0;
        if (!a) {
          const e = [yi.bind(this), void 0];
          e.unshift.apply(e, i), e.push.apply(e, l), u = e.length, c = Promise.resolve(t);
          while (f < u) c = c.then(e[f++], e[f++]);
          return c
        }
        u = i.length;
        let d = t;
        f = 0;
        while (f < u) {
          const e = i[f++],
            t = i[f++];
          try {
            d = e(d)
          } catch (h) {
            t.call(this, h);
            break
          }
        }
        try {
          c = yi.call(this, d)
        } catch (h) {
          return Promise.reject(h)
        }
        f = 0, u = l.length;
        while (f < u) c = c.then(l[f++], l[f++]);
        return c
      }
      getUri(e) {
        e = Ko(this.defaults, e);
        const t = qo(e.baseURL, e.url);
        return Ks(t, e.params, e.paramsSerializer)
      }
    }
    Os.forEach(["delete", "get", "head", "options"], (function (e) {
      Ei.prototype[e] = function (t, n) {
        return this.request(Ko(n || {}, {
          method: e,
          url: t,
          data: (n || {}).data
        }))
      }
    })), Os.forEach(["post", "put", "patch"], (function (e) {
      function t(t) {
        return function (n, r, s) {
          return this.request(Ko(s || {}, {
            method: e,
            headers: t ? {
              "Content-Type": "multipart/form-data"
            } : {},
            url: n,
            data: r
          }))
        }
      }
      Ei.prototype[e] = t(), Ei.prototype[e + "Form"] = t(!0)
    }));
    var Ti = Ei;
    class Ci {
      constructor(e) {
        if ("function" !== typeof e) throw new TypeError("executor must be a function.");
        let t;
        this.promise = new Promise((function (e) {
          t = e
        }));
        const n = this;
        this.promise.then((e => {
          if (!n._listeners) return;
          let t = n._listeners.length;
          while (t-- > 0) n._listeners[t](e);
          n._listeners = null
        })), this.promise.then = e => {
          let t;
          const r = new Promise((e => {
            n.subscribe(e), t = e
          })).then(e);
          return r.cancel = function () {
            n.unsubscribe(t)
          }, r
        }, e((function (e, r, s) {
          n.reason || (n.reason = new Fo(e, r, s), t(n.reason))
        }))
      }
      throwIfRequested() {
        if (this.reason) throw this.reason
      }
      subscribe(e) {
        this.reason ? e(this.reason) : this._listeners ? this._listeners.push(e) : this._listeners = [e]
      }
      unsubscribe(e) {
        if (!this._listeners) return;
        const t = this._listeners.indexOf(e); - 1 !== t && this._listeners.splice(t, 1)
      }
      toAbortSignal() {
        const e = new AbortController,
          t = t => {
            e.abort(t)
          };
        return this.subscribe(t), e.signal.unsubscribe = () => this.unsubscribe(t), e.signal
      }
      static source() {
        let e;
        const t = new Ci((function (t) {
          e = t
        }));
        return {
          token: t,
          cancel: e
        }
      }
    }
    var Oi = Ci;

    function Ri(e) {
      return function (t) {
        return e.apply(null, t)
      }
    }

    function Fi(e) {
      return Os.isObject(e) && !0 === e.isAxiosError
    }
    const Li = {
      Continue: 100,
      SwitchingProtocols: 101,
      Processing: 102,
      EarlyHints: 103,
      Ok: 200,
      Created: 201,
      Accepted: 202,
      NonAuthoritativeInformation: 203,
      NoContent: 204,
      ResetContent: 205,
      PartialContent: 206,
      MultiStatus: 207,
      AlreadyReported: 208,
      ImUsed: 226,
      MultipleChoices: 300,
      MovedPermanently: 301,
      Found: 302,
      SeeOther: 303,
      NotModified: 304,
      UseProxy: 305,
      Unused: 306,
      TemporaryRedirect: 307,
      PermanentRedirect: 308,
      BadRequest: 400,
      Unauthorized: 401,
      PaymentRequired: 402,
      Forbidden: 403,
      NotFound: 404,
      MethodNotAllowed: 405,
      NotAcceptable: 406,
      ProxyAuthenticationRequired: 407,
      RequestTimeout: 408,
      Conflict: 409,
      Gone: 410,
      LengthRequired: 411,
      PreconditionFailed: 412,
      PayloadTooLarge: 413,
      UriTooLong: 414,
      UnsupportedMediaType: 415,
      RangeNotSatisfiable: 416,
      ExpectationFailed: 417,
      ImATeapot: 418,
      MisdirectedRequest: 421,
      UnprocessableEntity: 422,
      Locked: 423,
      FailedDependency: 424,
      TooEarly: 425,
      UpgradeRequired: 426,
      PreconditionRequired: 428,
      TooManyRequests: 429,
      RequestHeaderFieldsTooLarge: 431,
      UnavailableForLegalReasons: 451,
      InternalServerError: 500,
      NotImplemented: 501,
      BadGateway: 502,
      ServiceUnavailable: 503,
      GatewayTimeout: 504,
      HttpVersionNotSupported: 505,
      VariantAlsoNegotiates: 506,
      InsufficientStorage: 507,
      LoopDetected: 508,
      NotExtended: 510,
      NetworkAuthenticationRequired: 511
    };
    Object.entries(Li).forEach((([e, t]) => {
      Li[t] = e
    }));
    var Pi = Li;

    function Ni(e) {
      const t = new Ti(e),
        n = Sr(Ti.prototype.request, t);
      return Os.extend(n, Ti.prototype, t, {
        allOwnKeys: !0
      }), Os.extend(n, t, null, {
        allOwnKeys: !0
      }), n.create = function (t) {
        return Ni(Ko(e, t))
      }, n
    }
    const Ai = Ni(po);
    Ai.Axios = Ti, Ai.CanceledError = Fo, Ai.CancelToken = Oi, Ai.isCancel = Oo, Ai.VERSION = bi, Ai.toFormData = Us, Ai.AxiosError = Ps, Ai.Cancel = Ai.CanceledError, Ai.all = function (e) {
      return Promise.all(e)
    }, Ai.spread = Ri, Ai.isAxiosError = Fi, Ai.mergeConfig = Ko, Ai.AxiosHeaders = To, Ai.formToJSON = e => uo(Os.isHTMLForm(e) ? new FormData(e) : e), Ai.getAdapter = gi.getAdapter, Ai.HttpStatusCode = Pi, Ai.default = Ai;
    var Mi = Ai;
    Number.prototype.countDecimals = function () {
      return Math.floor(this.valueOf()) === this.valueOf() ? 0 : this.toString().split(".")[1].length || 0
    };
    var Bi = {
      name: "Main",
      data: () => ({
        showSavedSettings: !1,
        savedSettings: [],
        pair: "",
        timeframe: "",
        pairs: [],
        timeframes: [],
        fields: [{
          idx: 0,
          fieldNo: null,
          stepSize: null,
          from: null,
          to: null,
          currentValue: 0
        }],
        formFields: [],
        isRunning: !1,
        canceled: !1,
        currentState: [],
        currentField: 0,
        currentBestSettings: null,
        bestSettings: [],
        resumeObject: null,
        resumeField: 0,
        resumeOldState: !1,
        stepCount: 0,
        timespan: "",
        avgTimespan: "",
        timestamps: [],
        t0: performance.now(),
        currentStep: 0,
        currentTf: 0,
        currentPair: 0,
        error: !1,
        errorMsg: "",


        searchSettings: 0,
        threshold: null,
        drawDownThreshold: null,
        minNumOfTradesThreshold: null,
        waitTimespan: null,
        showDrawDown: !1,
        showNumOfTrades: !1,

        searchMode: "sequential",
        searchRounds: null,
        smartFields: [],
        currentBestSmartValue: null,
        currentSmartRound: 1,
        bestSmartBacktestValue: null,
        resumeBestSmartBacktestValue: null,
        resumeSmartRound: null,
        numberOfFieldsWithNoImprovement: 0,
        resumeNumberOfFieldsWithNoImprovement: null,
        showEarlyStoppageHint: !1
      }),
      methods: {
        downloadCsv: function () {
          let e = 0,
            t = !0;
          this.bestSettings.forEach((n => {
            e = null != n.formFields ? Math.max(e, n.formFields.length) : Math.max(e, n.fields.length), null == n.totalTrades && (t = !1)
          }));
          let n = t ? "Pair;Timeframe;NetProfit;PercentProfitable;ProfitFactor;NumOfTrades;DrawDown;SharpeRatio;SortinoRatio;" : "Pair;Timeframe;BacktestResult;PercentProfitable;ProfitFactor;NumOfTrades;DrawDown;SharpeRatio;SortinoRatio;";
          for (let i = 0; i < e; i++) n += "FieldNumber;Value;";
          n += "\n", this.bestSettings.forEach((t => {
            e = Math.max(e, t.fields.length)
          })), this.bestSettings.forEach((e => {
            let t = null != e.backtestValue ? parseFloat(e.backtestValue).toFixed(2) : "",
              r = null != e.drawDown ? parseFloat(e.drawDown).toFixed(2) : "",
              s = null != e.netProfit ? parseFloat(100 * e.netProfit).toFixed(2) : "",
              o = null != e.percentProfit ? parseFloat(100 * e.percentProfit).toFixed(2) : "",
              i = null != e.profitFactor ? parseFloat(e.profitFactor).toFixed(3) : "",
              a = null != e.totalTrades ? e.totalTrades : "",
              l = null != e.sharpRatio ? parseFloat(e.sharpRatio).toFixed(3) : "",
              c = null != e.sortinoRatio ? parseFloat(e.sortinoRatio).toFixed(3) : "";
            n += "" == s ? `${e.pair};${e.tf};${t};${o};${i};${a};${r};${l};${c};` : `${e.pair};${e.tf};${s};${o};${i};${a};${r};${l};${c};`, null != e.formFields ? e.formFields.forEach((e => {
              n += `${e.fieldNo};${e.currentValue};`
            })) : e.fields.forEach((e => {
              n += `${e.fieldNo};${e.currentValue};`
            })), n += "\n"
          }));
          var r = new Blob([n], {
              type: "text/csv;charset=utf-8;"
            }),
            s = document.createElement("a");
          if (void 0 !== s.download) {
            var o = URL.createObjectURL(r);
            s.setAttribute("href", o), s.setAttribute("download", "backtest-results.csv"), s.style.visibility = "hidden", document.body.appendChild(s), s.click(), document.body.removeChild(s)
          }
        },
        addField: function () {
          console.log("🔥 addField method called!");
          console.log("Adding field, current fields:", this.fields.length);

          // Force Vue reactivity by creating a new array
          const newField = {
            idx: this.fields.length,
            fieldNo: null,
            stepSize: null,
            from: null,
            to: null
          };

          // Use Vue's reactive array methods
          this.fields.push(newField);

          // Force re-render by updating a reactive property
          this.$forceUpdate();

          console.log("After adding field, fields:", this.fields.length);
          this.saveCurrentState();
          this.calculateSteps();
          console.log("✅ addField completed");
        },
        removeField: function () {
          this.fields.splice(this.fields.length - 1, 1), this.saveCurrentState(), this.calculateSteps()
        },
        saveSettings: function () {
          var e = prompt("Enter description", "");
          if (null != e) {
            var t = {
                pair: this.pair,
                timeframe: this.timeframe,
                fields: this.fields,
                bestSettings: this.bestSettings
              },
              n = JSON.stringify(t);
            localStorage.setItem("save_" + e, n)
          }
        },
        loadSettings: function () {
          for (var e in this.savedSettings = [], localStorage) e.startsWith("save_") && this.savedSettings.push(e.replace("save_", ""));
          this.showSavedSettings = !0
        },
        saveCurrentState: function () {
          var e = {
              pair: this.pair,
              timeframe: this.timeframe,
              fields: this.fields,
              searchRounds: this.searchRounds
            },
            t = JSON.stringify(e);
          localStorage.setItem("lastState", t)
        },
        loadLastState: function () {
          const e = localStorage.getItem("lastState");
          if (null != e) {
            var t = JSON.parse(e);
            this.fields = t.fields, this.pair = t.pair, this.timeframe = t.timeframe;
            const n = parseInt(t.searchRounds);
            isNaN(n) || (this.searchRounds = n)
          }
          const n = localStorage.getItem("bestSettings");
          null != n && (this.bestSettings = JSON.parse(n))
        },
        closeLoadingWindows: function () {
          this.showSavedSettings = !1
        },
        load: function (e) {
          var t = JSON.parse(localStorage.getItem("save_" + e));
          this.fields = t.fields, this.pair = t.pair, this.timeframe = t.timeframe, this.bestSettings = t.bestSettings, this.showSavedSettings = !1, this.saveCurrentState()
        },
        removeSetting: function (e) {
          var t = confirm("Delete entry?");
          t && (localStorage.removeItem("save_" + e), this.loadSettings())
        },
        changePair: async function (e = !0) {
          console.log("🚀 changePair method called!");
          if (this.isRunning && e) this.canceled = !0;
          else {
            // First calculate steps to populate pairs and timeframes arrays
            console.log("Calculating steps...");
            this.calculateSteps();

            console.log("Pairs:", this.pairs);
            console.log("Timeframes:", this.timeframes);
            console.log("Fields:", this.fields);

            // Validate that we have pairs, timeframes, and fields
            if (this.pairs.length === 0 || this.timeframes.length === 0 || this.fields.length === 0) {
              console.log("❌ Validation failed: missing required fields");
              this.error = !0;
              this.errorMsg = "Please fill in all required fields: pairs, timeframes, and at least one parameter field.";
              return;
            }

            // Validate that all fields have valid values
            for (let field of this.fields) {
              if (!field.fieldNo || !field.stepSize || !field.from || !field.to) {
                console.log("❌ Validation failed: incomplete field", field);
                this.error = !0;
                this.errorMsg = "Please fill in all parameter fields (Field #, Step Size, From, To).";
                return;
              }
            }

            console.log("✅ All validations passed!");

            this.t0 = performance.now(), this.error = !1, this.errorMsg = "", this.showEarlyStoppageHint = !1, e && (this.timestamps = [], this.isRunning = !0, this.saveCurrentState()), this.searchSettings = localStorage.getItem("searchSettings"), null == this.searchSettings ? this.searchSettings = 0 : this.searchSettings = parseInt(this.searchSettings), !this.resumeOldState && e && (this.setFieldsToStart(this.fields.length), this.currentField = 0, this.currentStep = 0, this.currentTf = 0, this.currentPair = 0);
            var t = localStorage.getItem("threshold");
            null != t && (t = parseFloat(t), isNaN(t) || (this.threshold = t));
            var n = localStorage.getItem("drawDownThreshold");
            null != n && (n = parseFloat(n), isNaN(n) || (this.drawDownThreshold = n));
            var r = localStorage.getItem("minNumOfTradesThreshold");
            null != r && (r = parseInt(r), isNaN(r) || (this.minNumOfTradesThreshold = r));
            var s = localStorage.getItem("waitTimespan");
            if (null != s && (this.waitTimespan = s), "smart" != this.searchMode || this.resumeOldState || (this.currentSmartRound = 1, this.numberOfFieldsWithNoImprovement = 0, this.bestSmartBacktestValue = null), "smart" == this.searchMode) {
              const e = parseInt(this.searchRounds);
              this.searchRounds = isNaN(e) ? 1 : e
            }
            console.log("Starting changePair with pairs:", this.pairs, "timeframes:", this.timeframes);
            var o = this;
            chrome.tabs.query({
              currentWindow: !0,
              active: !0
            }, (function (e) {
              console.log("Sending change_pair message with pair:", o.pairs[o.currentPair]);
              chrome.tabs.sendMessage(e[0].id, {
                type: "change_pair",
                pair: o.pairs[o.currentPair]
              }, o.receiveChangePairResponse)
            }))
          }
        },
        receiveChangePairResponse: function () {
          this.changeTimeframe()
        },
        changeTimeframe: function () {
          var e = this;
          chrome.tabs.query({
            currentWindow: !0,
            active: !0
          }, (function (t) {
            chrome.tabs.sendMessage(t[0].id, {
              type: "change_tf",
              tf: e.timeframes[e.currentTf]
            }, e.receiveChangeTimeframeResponse)
          }))
        },
        receiveChangeTimeframeResponse: function (e) {
          this.formFields = e, this.runBacktest()
        },
        getWaitTimespan: function () {
          if (!this.waitTimespan) return null;
          if (this.waitTimespan.includes("-")) {
            const e = this.waitTimespan.split("-");
            if (e.length > 1) {
              let t = parseFloat(e[0]);
              const n = parseFloat(e[1]);
              return isNaN(t) || isNaN(n) ? 1 : Math.floor(Math.random() * (n - t + 1)) + t
            }
            return 1
          } {
            let e = parseFloat(this.waitTimespan);
            return isNaN(e) ? null : e
          }
        },
        changeValue: function () {
          var e = this;
          const t = e.getWaitTimespan();
          chrome.tabs.query({
            currentWindow: !0,
            active: !0
          }, (function (n) {
            chrome.tabs.sendMessage(n[0].id, {
              type: "change_value",
              fields: e.fields,
              waitTimespan: t
            }, e.receiveChangeValueResponse)
          }))
        },
        receiveChangeValueResponse: function (e) {
          if (this.canceled) return this.isRunning = !1, this.canceled = !1, this.resumeObject = null, this.resumeField = 0, this.currentBestSettings.timestamp = new Date, this.bestSettings.push(this.currentBestSettings), this.sortBestSettings(), localStorage.setItem("bestSettings", JSON.stringify(this.bestSettings)), this.currentBestSettings = null, localStorage.removeItem("resume"), localStorage.removeItem("resumeField"), localStorage.removeItem("resumeBestSettings"), localStorage.removeItem("currentStep"), localStorage.removeItem("currentTf"), void localStorage.removeItem("currentPair");
          e.error && (this.error = !0, this.errorMsg = e.errorMsg);
          let t = parseFloat(this.fields[this.currentField].currentValue);
          if (null != e && !e.error) {
            this.currentStep++;
            var n = performance.now();
            const r = n - this.t0;
            this.t0 = performance.now(), this.timestamps.push(r), this.timestamps.length > 250 && this.timestamps.shift(), this.avgTimespan = (this.timestamps.reduce(((e, t) => e + t), 0) / this.timestamps.length).toFixed(0) / 1e3;
            const s = this.stepCount - this.currentStep;
            let o;
            switch (this.timespan = this.toTime(s * this.avgTimespan, !0), this.searchSettings) {
              case 0:
                o = e.result.netProfitAll;
                break;
              case 1:
                o = e.result.netProfitLong;
                break;
              case 2:
                o = e.result.netProfitShort;
                break;
              case 3:
                o = e.result.percentProfitAll;
                break;
              case 4:
                o = e.result.profitFactor;
                break;
              default:
                o = e.result.netProfitAll
            }
            let i = parseFloat(o),
              a = 100 * parseFloat(e.result.drawDown),
              l = parseInt(e.result.totalTrades),
              c = e.result.sharpRatio ? parseFloat(e.result.sharpRatio) : 0,
              u = e.result.sortinoRatio ? parseFloat(e.result.sortinoRatio) : 0;
            if (this.searchSettings <= 3 && (i *= 100), null == this.currentBestSettings || i > this.currentBestSettings.backtestValue || null != this.threshold && i >= this.threshold) {
              let n = [];
              for (let e = 0; e < this.fields.length; e++) {
                const t = this.fields[e],
                  r = {
                    idx: t.idx,
                    fieldNo: t.fieldNo,
                    stepSize: t.stepSize,
                    from: t.from,
                    to: t.to,
                    currentValue: t.currentValue
                  };
                n.push(r)
              }("smart" == this.searchMode && null == this.bestSmartBacktestValue || i > this.bestSmartBacktestValue) && (this.currentBestSmartValue = t, this.bestSmartBacktestValue = i);
              let r = [];
              for (let e = 0; e < this.formFields.length; e++) {
                const t = this.formFields[e];
                let s = n.find((e => parseInt(e.fieldNo) === parseInt(t.fieldNo)));
                "undefined" === typeof s ? r.push(t) : r.push(s)
              }(null == this.drawDownThreshold || a <= this.drawDownThreshold) && (null == this.minNumOfTradesThreshold || l >= this.minNumOfTradesThreshold) && (null != this.threshold && i >= this.threshold ? (this.bestSettings.push({
                pair: this.pairs[this.currentPair],
                tf: this.timeframes[this.currentTf],
                backtestValue: i,
                fields: JSON.parse(JSON.stringify(n)),
                timestamp: new Date,
                isNotPercentValue: this.searchSettings > 3,
                drawDown: a,
                formFields: r,
                netProfit: parseFloat(e.result.netProfitAll),
                percentProfit: parseFloat(e.result.percentProfitAll),
                profitFactor: parseFloat(e.result.profitFactor),
                totalTrades: l,
                sharpRatio: c,
                sortinoRatio: u
              }), this.sortBestSettings(), localStorage.setItem("bestSettings", JSON.stringify(this.bestSettings))) : null == this.threshold && (this.currentBestSettings = {
                pair: this.pairs[this.currentPair],
                tf: this.timeframes[this.currentTf],
                backtestValue: i,
                fields: JSON.parse(JSON.stringify(n)),
                timestamp: new Date,
                isNotPercentValue: this.searchSettings > 3,
                drawDown: a,
                formFields: r,
                netProfit: parseFloat(e.result.netProfitAll),
                percentProfit: parseFloat(e.result.percentProfitAll),
                profitFactor: parseFloat(e.result.profitFactor),
                totalTrades: l,
                sharpRatio: c,
                sortinoRatio: u
              }))
            }
          }
          let r = parseFloat(this.fields[this.currentField].stepSize),
            s = r.countDecimals();
          if (t + r > parseFloat(this.fields[this.currentField].to))
            if ("smart" == this.searchMode) {
              if (null != this.currentBestSmartValue) this.fields[this.currentField].currentValue = this.currentBestSmartValue, this.fields[this.currentField].bestSmartValue = this.currentBestSmartValue, this.numberOfFieldsWithNoImprovement = 0;
              else {
                this.numberOfFieldsWithNoImprovement++;
                const e = this.fields[this.currentField].bestSmartValue;
                this.fields[this.currentField].currentValue = isNaN(e) ? parseFloat(this.fields[this.currentField].from) : e
              }
              if (this.currentBestSmartValue = null, this.currentField++, this.currentField >= this.fields.length)
                if (this.currentSmartRound + 1 > this.searchRounds) {
                  if (this.checkForBacktestEnd()) return
                } else this.currentSmartRound++, this.currentField = 0;
              if (this.numberOfFieldsWithNoImprovement == this.fields.length && (this.showEarlyStoppageHint = !0, this.checkForBacktestEnd(!0))) return;
              this.fields[this.currentField].currentValue = parseFloat(this.fields[this.currentField].from)
            } else {
              if (this.currentField++, this.checkForBacktestEnd()) return;
              while (this.currentField < this.fields.length && parseFloat(this.fields[this.currentField].currentValue) + parseFloat(this.fields[this.currentField].stepSize) > parseFloat(this.fields[this.currentField].to)) this.currentField++;
              if (this.checkForBacktestEnd()) return;
              t = parseFloat(this.fields[this.currentField].currentValue), r = parseFloat(this.fields[this.currentField].stepSize);
              let e = r.countDecimals();
              this.fields[this.currentField].currentValue = (t + r).toFixed(e), this.setFieldsToStart(this.currentField), this.currentField = 0
            }
          else this.fields[this.currentField].currentValue = (t + r).toFixed(s);
          localStorage.setItem("resume", JSON.stringify(this.fields)), localStorage.setItem("resumeField", this.currentField), localStorage.setItem("resumeRound", this.currentSmartRound), localStorage.setItem("resumeNumberOfFieldsWithNoImprovement", this.numberOfFieldsWithNoImprovement), localStorage.setItem("resumeBestSmartBacktestValue", this.bestSmartBacktestValue), localStorage.setItem("currentStep", this.currentStep), localStorage.setItem("currentTf", this.currentTf), localStorage.setItem("currentPair", this.currentPair), localStorage.setItem("resumeBestSettings", JSON.stringify(this.currentBestSettings)), this.runBacktest()
        },
        checkForBacktestEnd: function (e = !1) {
          let t = !1;
          return (this.currentField >= this.fields.length || e) && (this.currentTf + 1 < this.timeframes.length ? (this.currentTf++, this.currentField = 0, this.currentSmartRound = 1, this.numberOfFieldsWithNoImprovement = 0, this.setFieldsToStart(this.fields.length), this.changeTimeframe(), t = !0) : this.currentPair + 1 < this.pairs.length ? (this.currentPair++, this.currentField = 0, this.currentTf = 0, this.currentSmartRound = 1, this.numberOfFieldsWithNoImprovement = 0, this.setFieldsToStart(this.fields.length), this.changePair(!1), t = !0) : (this.isRunning = !1, this.canceled = !1, this.resumeObject = null, this.resumeField = 0, this.resumeSmartRound = null, this.resumeField, this.numberOfFieldsWithNoImprovement = 0, t = !0), t && (null != this.currentBestSettings && (this.currentBestSettings.timestamp = new Date, this.bestSettings.push(this.currentBestSettings)), this.sortBestSettings(), localStorage.setItem("bestSettings", JSON.stringify(this.bestSettings)), this.currentBestSettings = null, localStorage.removeItem("resume"), localStorage.removeItem("resumeField"), localStorage.removeItem("resumeBestSettings"), localStorage.removeItem("resumeRound"), localStorage.removeItem("resumeNumberOfFieldsWithNoImprovement"), localStorage.removeItem("resumeBestSmartBacktestValue"), localStorage.removeItem("currentStep"), localStorage.removeItem("currentTf"), localStorage.removeItem("currentPair"))), t
        },
        runBacktest: function () {
          this.changeValue()
        },
        sleep: function (e) {
          return new Promise((t => setTimeout(t, e)))
        },
        setFieldsToStart: function (e) {
          for (let t = 0; t < e; t++) this.fields[t].currentValue = parseFloat(this.fields[t].from)
        },
        changeBacktestSettings: function (e, t, n, r) {
          this.pair = e, this.timeframe = t, this.fields = JSON.parse(JSON.stringify(n)), chrome.tabs.query({
            currentWindow: !0,
            active: !0
          }, (function (s) {
            chrome.tabs.sendMessage(s[0].id, {
              type: "change_pair",
              pair: e
            }, (() => {
              chrome.tabs.query({
                currentWindow: !0,
                active: !0
              }, (function (e) {
                chrome.tabs.sendMessage(e[0].id, {
                  type: "change_tf",
                  tf: t
                }, (() => {
                  chrome.tabs.query({
                    currentWindow: !0,
                    active: !0
                  }, (function (e) {
                    chrome.tabs.sendMessage(e[0].id, {
                      type: "change_value",
                      fields: null != r ? r : n
                    })
                  }))
                }))
              }))
            }))
          }))
        },


        resumeState: function () {
          this.fields = this.resumeObject, this.currentField = this.resumeField, this.currentStep = this.resumeCurrentStep, this.currentTf = this.resumeCurrentTf, this.currentPair = this.resumeCurrentPair, this.currentBestSettings = this.resumeBestSettings, this.currentSmartRound = this.resumeSmartRound, this.numberOfFieldsWithNoImprovement = this.resumeNumberOfFieldsWithNoImprovement, this.bestSmartBacktestValue = this.resumeBestSmartBacktestValue, this.resumeOldState = !0
        },
        removeBacktest: function (e) {
          var t = confirm("Delete this entry?");
          t && (this.bestSettings.splice(e, 1), localStorage.setItem("bestSettings", JSON.stringify(this.bestSettings)))
        },
        removeAllBacktest: function () {
          var e = confirm("Remove all entries?");
          e && (this.bestSettings = [], localStorage.setItem("bestSettings", JSON.stringify(this.bestSettings)), localStorage.removeItem("bestSettings"))
        },
        calculateSteps: function () {
          this.saveCurrentState(), this.timeframes = [], this.pairs = [], this.pair.includes(",") ? (this.pairs = this.pair.split(","), this.pairs.map((e => e.trim()))) : this.pairs.push(this.pair.trim()), this.timeframe.includes(",") ? (this.timeframes = this.timeframe.split(","), this.timeframes.map((e => e.trim()))) : this.timeframes.push(this.timeframe.trim());
          let e = !0,
            t = [];
          this.currentStep = 0;
          for (let n = 0; n < this.fields.length; n++) {
            const r = this.fields[n],
              s = parseFloat(r.stepSize),
              o = parseFloat(r.from),
              i = parseFloat(r.to);
            if (isNaN(s) || isNaN(o) || isNaN(i)) {
              e = !1;
              break
            }
            const a = parseInt(((i - o + s) / s).toFixed(0));
            t.push(a)
          }
          if (e) {
            if (t.length > 1) {
              this.stepCount = t[0];
              for (let e = 1; e < t.length; e++) "sequential" == this.searchMode ? this.stepCount *= t[e] : this.stepCount += t[e]
            } else this.stepCount = t[0];
            if (this.stepCount *= this.pairs.length, this.stepCount *= this.timeframes.length, "smart" == this.searchMode) {
              const e = parseInt(this.searchRounds);
              this.stepCount *= isNaN(e) ? 1 : e
            }
            this.stepCount > 0 ? this.timespan = this.toTime(this.stepCount, !0) : (this.stepCount = 0, this.timespan = "")
          } else this.stepCount = 0
        },
        toTime: function (e, t) {
          var n = t ? 1e3 * e : e,
            r = ~(4 * !!t),
            s = new Date(n).toISOString().slice(11, r);
          if (n >= 864e5) {
            var o = s.split(/:(?=\d{2}:)/);
            return o[0] -= -24 * (n / 864e5 | 0), o.join(":")
          }
          return s
        },
        sortBestSettings: function (e = !1, t = !1) {
          this.bestSettings = t ? this.bestSettings.sort(((e, t) => parseFloat(e.drawDown) - parseFloat(t.drawDown))) : e ? this.bestSettings.sort(((e, t) => parseFloat(t.backtestValue) - parseFloat(e.backtestValue))) : this.bestSettings.sort(((e, t) => new Date(t.timestamp) - new Date(e.timestamp)))
        },

      },
      mounted: function () {
        this.loadLastState();
        const e = localStorage.getItem("searchMode");
        null != e && (this.searchMode = e), this.resumeObject = JSON.parse(localStorage.getItem("resume")), this.resumeField = parseInt(localStorage.getItem("resumeField")), this.resumeSmartRound = parseInt(localStorage.getItem("resumeRound")), this.resumeBestSmartBacktestValue = parseFloat(localStorage.getItem("resumeBestSmartBacktestValue")), this.resumeNumberOfFieldsWithNoImprovement = parseInt(localStorage.getItem("resumeNumberOfFieldsWithNoImprovement")), this.resumeCurrentStep = parseInt(localStorage.getItem("currentStep")), this.resumeCurrentTf = parseInt(localStorage.getItem("currentTf")), this.resumeCurrentPair = parseInt(localStorage.getItem("currentPair")), this.resumeBestSettings = JSON.parse(localStorage.getItem("resumeBestSettings")), this.showDrawDown = "true" === localStorage.getItem("showDrawDown"), this.showNumOfTrades = "true" === localStorage.getItem("showNumOfTrades"), this.calculateSteps(), this.sortBestSettings()
      }
    };
    n(6270);
    const ji = (0, ue.A)(Bi, [
      ["render", br],
      ["__scopeId", "data-v-3a57803b"]
    ]);
    var Di = ji,
      Ii = {
        name: "Home",
        components: {
          Main: Di,
          Nav: xn,
          Footer: Rn
        },
        data: () => ({
          testnet: !1
        }),
        methods: {},
        created: function () {}
      };
    const Ui = (0, ue.A)(Ii, [
      ["render", mn]
    ]);
    var Vi = Ui;
    const $i = {
        class: "settings"
      },
      Wi = {
        class: "modal-header d-flex justify-content-between align-items-center flex-row"
      },
      qi = {
        class: "content"
      },
      Xi = {
        class: "row"
      },
      Ki = {
        class: "col-12"
      },
      Hi = {
        class: "form-floating mt-3 mb-2"
      },
      zi = {
        class: "mb-2"
      },
      Gi = {
        class: "form-check ms-2"
      },
      Qi = {
        class: "form-check ms-2 mb-2"
      },
      Ji = {
        class: "form-check-label",
        for: "smart"
      },
      Zi = {
        key: 0,
        class: "mb-2"
      },
      Yi = {
        class: "form-check ms-2"
      },
      ea = {
        class: "form-check ms-2"
      },
      ta = {
        class: "form-check ms-2"
      },
      na = {
        class: "form-check ms-2"
      },
      ra = {
        class: "form-check ms-2 mb-2"
      },
      sa = {
        class: "form-check ms-2 mb-2"
      },
      oa = {
        class: "form-check ms-2 mb-2"
      },
      ia = {
        class: "col-12"
      },
      aa = {
        class: "form-floating mt-3 mb-2"
      },
      la = {
        class: "col-12"
      },
      ca = {
        class: "form-floating mt-3 mb-2"
      },
      ua = {
        class: "col-12"
      },
      fa = {
        class: "form-floating mt-3 mb-2"
      },
      da = {
        class: "col-12"
      },
      ha = {
        class: "form-floating mt-3 mb-2"
      },
      pa = {
        class: "col-12 text-center"
      };

    function ma(e, n, r, s, o, i) {
      return (0, t.uX)(), (0, t.CE)("div", $i, [(0, t.Lk)("div", Wi, [(0, t.Lk)("a", {
        href: "#",
        class: "back-button",
        onClick: n[0] || (n[0] = t => e.$router.push("/"))
      }, n[19] || (n[19] = [(0, t.Lk)("i", {
        class: "fas fa-chevron-left"
      }, null, -1)])), n[20] || (n[20] = (0, t.Lk)("h1", {
        class: "logo m-0 flex-grow-1 text-center"
      }, "Settings", -1))]), (0, t.Lk)("div", qi, [(0, t.Lk)("div", Xi, [ n[43] || (n[43] = (0, t.Lk)("p", {
        class: "mt-2"
      }, "Search mode:", -1)), (0, t.Lk)("div", Gi, [(0, t.bo)((0, t.Lk)("input", {
        class: "form-check-input",
        type: "radio",
        name: "searchMode",
        id: "sequential",
        value: "sequential",
        "onUpdate:modelValue": n[3] || (n[3] = t => e.searchMode = t)
      }, null, 512), [
        [Q, e.searchMode]
      ]), n[26] || (n[26] = (0, t.Lk)("label", {
        class: "form-check-label",
        for: "sequential"
      }, " Sequential ", -1))]), (0, t.Lk)("div", Qi, [(0, t.bo)((0, t.Lk)("input", {
        class: "form-check-input",
        type: "radio",
        name: "searchMode",
        id: "smart",
        value: "smart",
        "onUpdate:modelValue": n[4] || (n[4] = t => e.searchMode = t)
      }, null, 512), [
        [Q, e.searchMode]
      ]), (0, t.Lk)("label", Ji, [n[28] || (n[28] = (0, t.eW)(" Smart search ")), (0, t.Lk)("a", {
        href: "#",
        onClick: n[5] || (n[5] = t => e.showSearchHint = !e.showSearchHint)
      }, n[27] || (n[27] = [(0, t.Lk)("i", {
        class: "ms-1 fas fa-info-circle"
      }, null, -1)]))])]), e.showSearchHint ? ((0, t.uX)(), (0, t.CE)("small", Zi, n[29] || (n[29] = [(0, t.eW)("This option allows you to find good settings based on a weighted search much faster. Especially when you have a lot of fields to test and therefore a huge number of steps. It runs through all fields one by one and always keep the best settings for each field. Then you are able to repeat this process multiple times by defining the number of runs.")]))) : (0, t.Q3)("", !0), n[44] || (n[44] = (0, t.Lk)("hr", null, null, -1)), n[45] || (n[45] = (0, t.Lk)("p", {
        class: "mt-2"
      }, "Search settings:", -1)), (0, t.Lk)("div", Yi, [(0, t.bo)((0, t.Lk)("input", {
        class: "form-check-input",
        type: "radio",
        name: "searchSettings",
        id: "netProfit",
        value: "0",
        "onUpdate:modelValue": n[6] || (n[6] = t => e.searchSettings = t)
      }, null, 512), [
        [Q, e.searchSettings]
      ]), n[30] || (n[30] = (0, t.Lk)("label", {
        class: "form-check-label",
        for: "netProfit"
      }, " Net Profit ", -1))]), (0, t.Lk)("div", ea, [(0, t.bo)((0, t.Lk)("input", {
        class: "form-check-input",
        type: "radio",
        name: "searchSettings",
        id: "netProfitLong",
        value: "1",
        "onUpdate:modelValue": n[7] || (n[7] = t => e.searchSettings = t)
      }, null, 512), [
        [Q, e.searchSettings]
      ]), n[31] || (n[31] = (0, t.Lk)("label", {
        class: "form-check-label",
        for: "netProfitLong"
      }, " Net Profit: Long only ", -1))]), (0, t.Lk)("div", ta, [(0, t.bo)((0, t.Lk)("input", {
        class: "form-check-input",
        type: "radio",
        name: "searchSettings",
        id: "netProfitShort",
        value: "2",
        "onUpdate:modelValue": n[8] || (n[8] = t => e.searchSettings = t)
      }, null, 512), [
        [Q, e.searchSettings]
      ]), n[32] || (n[32] = (0, t.Lk)("label", {
        class: "form-check-label",
        for: "netProfitShort"
      }, " Net Profit: Short only ", -1))]), (0, t.Lk)("div", na, [(0, t.bo)((0, t.Lk)("input", {
        class: "form-check-input",
        type: "radio",
        name: "searchSettings",
        id: "profitable",
        value: "3",
        "onUpdate:modelValue": n[9] || (n[9] = t => e.searchSettings = t)
      }, null, 512), [
        [Q, e.searchSettings]
      ]), n[33] || (n[33] = (0, t.Lk)("label", {
        class: "form-check-label",
        for: "profitable"
      }, " Percent Profitable ", -1))]), (0, t.Lk)("div", ra, [(0, t.bo)((0, t.Lk)("input", {
        class: "form-check-input",
        type: "radio",
        name: "searchSettings",
        id: "profitFactor",
        value: "4",
        "onUpdate:modelValue": n[10] || (n[10] = t => e.searchSettings = t)
      }, null, 512), [
        [Q, e.searchSettings]
      ]), n[34] || (n[34] = (0, t.Lk)("label", {
        class: "form-check-label",
        for: "profitFactor"
      }, " Profit Factor ", -1))]), n[46] || (n[46] = (0, t.Lk)("hr", null, null, -1)), (0, t.Lk)("div", sa, [(0, t.bo)((0, t.Lk)("input", {
        class: "form-check-input",
        type: "checkbox",
        name: "dd",
        id: "dd",
        "onUpdate:modelValue": n[11] || (n[11] = t => e.showDrawDown = t)
      }, null, 512), [
        [z, e.showDrawDown]
      ]), n[35] || (n[35] = (0, t.Lk)("label", {
        class: "form-check-label",
        for: "dd"
      }, " Show Draw Down ", -1))]), (0, t.Lk)("div", oa, [(0, t.bo)((0, t.Lk)("input", {
        class: "form-check-input",
        type: "checkbox",
        name: "dd",
        id: "dd",
        "onUpdate:modelValue": n[12] || (n[12] = t => e.showNumOfTrades = t)
      }, null, 512), [
        [z, e.showNumOfTrades]
      ]), n[36] || (n[36] = (0, t.Lk)("label", {
        class: "form-check-label",
        for: "dd"
      }, " Show # of trades ", -1))]), (0, t.Lk)("div", ia, [(0, t.Lk)("div", aa, [(0, t.bo)((0, t.Lk)("input", {
        type: "text",
        class: "form-control",
        id: "threshold",
        placeholder: "Threshold",
        "onUpdate:modelValue": n[13] || (n[13] = t => e.threshold = t)
      }, null, 512), [
        [H, e.threshold]
      ]), n[37] || (n[37] = (0, t.Lk)("label", {
        for: "threshold"
      }, "Threshold", -1))])]), n[47] || (n[47] = (0, t.Lk)("small", {
        class: "mb-2"
      }, 'You can define a threshold: everything above this value will be listed under "best findings".', -1)), (0, t.Lk)("div", la, [(0, t.Lk)("div", ca, [(0, t.bo)((0, t.Lk)("input", {
        type: "text",
        class: "form-control",
        id: "threshold",
        placeholder: "Max Draw Down Threshold",
        "onUpdate:modelValue": n[14] || (n[14] = t => e.drawDownThreshold = t)
      }, null, 512), [
        [H, e.drawDownThreshold]
      ]), n[38] || (n[38] = (0, t.Lk)("label", {
        for: "drawDownThreshold"
      }, "Max Draw Down Threshold", -1))])]), n[48] || (n[48] = (0, t.Lk)("small", {
        class: "mb-2"
      }, [(0, t.eW)("You can define a max draw down threshold: a finding will only be listed if it's value is "), (0, t.Lk)("strong", null, "below"), (0, t.eW)(" this threshold.")], -1)), (0, t.Lk)("div", ua, [(0, t.Lk)("div", fa, [(0, t.bo)((0, t.Lk)("input", {
        type: "text",
        class: "form-control",
        id: "threshold",
        placeholder: "Threshold",
        "onUpdate:modelValue": n[15] || (n[15] = t => e.minNumOfTradesThreshold = t)
      }, null, 512), [
        [H, e.minNumOfTradesThreshold]
      ]), n[39] || (n[39] = (0, t.Lk)("label", {
        for: "minNumOfTradesThreshold"
      }, "Min # of Trades Threshold", -1))])]), n[49] || (n[49] = (0, t.Lk)("small", {
        class: "mb-2"
      }, 'Every settings with number of trades above this threshold value will be listed under "best findings".', -1)), n[50] || (n[50] = (0, t.Lk)("hr", null, null, -1)), (0, t.Lk)("div", da, [(0, t.Lk)("div", ha, [(0, t.bo)((0, t.Lk)("input", {
        type: "text",
        class: "form-control",
        id: "threshold",
        placeholder: "Threshold",
        "onUpdate:modelValue": n[16] || (n[16] = t => e.waitTimespan = t)
      }, null, 512), [
        [H, e.waitTimespan]
      ]), n[40] || (n[40] = (0, t.Lk)("label", {
        for: "waitTimespan"
      }, "Wait timespan after each step", -1))])]), n[51] || (n[51] = (0, t.Lk)("small", {
        class: "mb-2"
      }, "You can define a delay in sec (for example 5 sec) or a random range (like 1-3). With this activated the extension waits this defined period of time after each step.", -1)), (0, t.Lk)("div", pa, [(0, t.Lk)("button", {
        class: "btn btn-secondary mr-2 mb-2",
        onClick: n[17] || (n[17] = e => i.onCancel())
      }, "Cancel"), n[41] || (n[41] = (0, t.eW)(" ")), (0, t.Lk)("button", {
        class: "ml-2 mb-2 btn btn-primary",
        onClick: n[18] || (n[18] = e => i.onSave())
      }, "Save")])])])])
    }
    var ga = {
      name: "Settings",
      data: () => ({
        searchSettings: 0,
        threshold: null,
        drawDownThreshold: null,
        showDrawDown: !1,
        showNumOfTrades: !1,
        minNumOfTradesThreshold: null,
        waitTimespan: null,
        searchMode: "sequential",
        showSearchHint: !1
      }),
      methods: {
        onCancel: function () {
          this.$router.push({
            name: "Home"
          })
        },
        onSave: function () {
          localStorage.setItem("searchSettings", this.searchSettings), localStorage.setItem("showDrawDown", this.showDrawDown), localStorage.setItem("showNumOfTrades", this.showNumOfTrades), localStorage.setItem("searchMode", this.searchMode), null != this.drawDownThreshold && localStorage.setItem("drawDownThreshold", this.drawDownThreshold), null != this.threshold && localStorage.setItem("threshold", this.threshold), null != this.minNumOfTradesThreshold && localStorage.setItem("minNumOfTradesThreshold", this.minNumOfTradesThreshold), null != this.waitTimespan && localStorage.setItem("waitTimespan", this.waitTimespan), this.$router.push({
            name: "Home"
          })
        }
      },
      created: function () {
        this.searchSettings = localStorage.getItem("searchSettings"), null == this.searchSettings && (this.searchSettings = 0);
        const e = localStorage.getItem("searchMode");
        null != e && (this.searchMode = e), this.threshold = localStorage.getItem("threshold"), this.drawDownThreshold = localStorage.getItem("drawDownThreshold"), this.minNumOfTradesThreshold = localStorage.getItem("minNumOfTradesThreshold"), this.waitTimespan = localStorage.getItem("waitTimespan"), this.showDrawDown = "true" === localStorage.getItem("showDrawDown"), this.showNumOfTrades = "true" === localStorage.getItem("showNumOfTrades")
      }
    };
    n(578);
    const va = (0, ue.A)(ga, [
      ["render", ma],
      ["__scopeId", "data-v-d54a9c32"]
    ]);
    var ya = va;
    const ba = [{
        path: "/settings",
        name: "Settings",
        component: ya
      }, {
        path: "/",
        name: "Home",
        component: Vi
      }],
      Sa = hn({
        history: vt(),
        routes: ba
      });
    var wa = Sa;
    console.log("🚀 EXTENSION SCRIPT STARTED");

    try {
      console.log("📦 Checking Vue availability...");
      console.log("oe function:", typeof oe);
      console.log("de object:", typeof de);
      console.log("wa router:", typeof wa);

      console.log("🔧 Creating Vue app...");
      const app = oe(de).use(wa);
      console.log("✅ Vue app created:", app);

      console.log("🎯 Mounting to #app...");
      const mountedApp = app.mount("#app");
      console.log("✅ Vue app mounted successfully:", mountedApp);

    } catch (error) {
      console.error("❌ ERROR during Vue initialization:", error);
      console.error("Error stack:", error.stack);
    }
  }()
})();