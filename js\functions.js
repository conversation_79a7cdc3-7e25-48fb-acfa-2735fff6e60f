function read() {
    sessionStorage.setItem("isLoading", !1)
}

function readData() {
    const e = TradingViewApi._chartWidgetCollection.activeChartWidget._value._model.m_model._activeStrategySource._value._statusView,
        t = e ? e._text : "";
    null == e || t.includes("loading...") ? window.setTimeout(readData, 50) : window.setTimeout((() => {
        if (null != TradingViewApi._chartWidgetCollection.activeChartWidget._value._model.m_model._activeStrategySource._value._reportData) {
            let e = TradingViewApi._chartWidgetCollection.activeChartWidget._value._model.m_model._activeStrategySource._value._reportData.performance;
            const t = e.all.netProfitPercent,
                a = e.all.percentProfitable,
                o = e.long.netProfitPercent,
                r = e.long.percentProfitable,
                n = e.short.netProfitPercent,
                l = e.short.percentProfitable,
                i = e.all.profitFactor,
                c = e.maxStrategyDrawDownPercent,
                s = e.all.avgBarsInTrade,
                d = e.all.totalTrades,
                g = e.sharpeRatio,
                m = e.sortinoRatio;
            localStorage.setItem("netProfitAll", t), localStorage.setItem("percentProfitAll", a), localStorage.setItem("netProfitLong", o), localStorage.setItem("percentProfitLong", r), localStorage.setItem("netProfitShort", n), localStorage.setItem("percentProfitShort", l), localStorage.setItem("profitFactor", i), localStorage.setItem("drawDown", c), localStorage.setItem("avgBars", s), localStorage.setItem("totalTrades", d), localStorage.setItem("sharpRatio", g), localStorage.setItem("sortinoRatio", m), localStorage.setItem("isLoading", !1), localStorage.setItem("block", !1), localStorage.setItem("error", !1)
        } else localStorage.setItem("isLoading", !1), localStorage.setItem("block", !1)
    }), 150)
}

function dispatchEnterKey() {
    const e = document.querySelectorAll('input[class^="search"]');
    if (e.length > 0) {
        const t = 13,
            a = "keydown",
            o = new KeyboardEvent(a, {
                key: "Enter",
                keyCode: t,
                charCode: t,
                code: "Enter",
                bubbles: !0,
                cancelable: !0,
                sourceCapabilities: new InputDeviceCapabilities({
                    firesTouchEvents: !1
                })
            });
        e[0].dispatchEvent(o)
    }
}
document.addEventListener("readTrades", (function (e) {
    readData()
})), document.addEventListener("read", (function (e) {
    read()
})), document.addEventListener("dispatchEnterKey", (function (e) {
    dispatchEnterKey()
}));