<!DOCTYPE html>
<html lang="en">
<head>
	<link rel="stylesheet" href="css/bootstrap.min.css">
	<link
      href="https://fonts.googleapis.com/css?family=Open+Sans"
      rel="stylesheet"
      type="text/css"
  	/>
	<!-- Fonts Awesome CSS -->
  <!-- <link rel="stylesheet" href="css/fontawesome.min.css"> -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" integrity="sha512-iBBXm8fW90+nuLcSKlbmrPcLa0OT92xO1BIsZ+ywDWZCvqsWgccV3gFoRBv0z+8dLJgyAHIhR35VZc2oM/gI1w==" crossorigin="anonymous" referrerpolicy="no-referrer" />
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Strategy Finder - Whats new</title>
</head>
<body>
	<div class="container">
		<header class="my-5">
            <h1 class="text-center"> <img src="icons/48.png" alt="logo" class="me-3"> TradingView Strategy Finder</h1>
        </header>
		
		<div class="content px-5 py-3">
			<h2>v.1.3.3 - Manifest upgrade to v3</h2>
            <p class="mt-3">To keep the extension aligned with Google’s latest guidelines, we've updated the manifest to Version 3. This ensures improved security, performance, and compatibility with Chrome's current standards.<br>
			</p>

			<h2>v.1.3.0 - Open Source Release</h2>
            <p class="mt-3">The TradingView Strategy Finder is now available as an open source tool for the community. All authentication and licensing requirements have been removed.</p>

			<h2>v.1.2.3 - Deep history support</h2>
            <p class="mt-3">Deep history support is now available. Just enable the "Deep Backtesting" checkbox, have the "Performance Summary" tab open and start the test run as usual.<br>
				<i>We would recommend to use a wait timespan after each step (in the settings of the extension) in order to protect your TV account.</i>
			</p>


        </div>
	</div>
</body>
</html>