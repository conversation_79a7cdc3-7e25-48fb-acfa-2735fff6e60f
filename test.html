<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Extension Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>TradingView Strategy Finder - Test Results</h1>
        <div id="test-results"></div>
        
        <!-- Test the popup content -->
        <div id="popup-container" style="width: 400px; height: 600px; border: 1px solid #ccc; margin-top: 20px;">
            <iframe src="popup.html" width="100%" height="100%" frameborder="0"></iframe>
        </div>
    </div>

    <script>
        function addTestResult(test, success, message) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${success ? 'success' : 'error'}`;
            resultDiv.innerHTML = `
                <strong>${test}:</strong> 
                <i class="fas fa-${success ? 'check' : 'times'}"></i> 
                ${message}
            `;
            resultsDiv.appendChild(resultDiv);
        }

        // Test 1: Check if popup.html loads without errors
        function testPopupLoad() {
            try {
                const iframe = document.querySelector('iframe');
                iframe.onload = function() {
                    try {
                        // Check if the iframe content loaded
                        addTestResult('Popup Load', true, 'popup.html loaded successfully');
                    } catch (e) {
                        addTestResult('Popup Load', false, `Error accessing iframe: ${e.message}`);
                    }
                };
                iframe.onerror = function() {
                    addTestResult('Popup Load', false, 'Failed to load popup.html');
                };
            } catch (e) {
                addTestResult('Popup Load', false, `Error setting up iframe test: ${e.message}`);
            }
        }

        // Test 2: Check if manifest.json is valid
        function testManifest() {
            fetch('manifest.json')
                .then(response => response.json())
                .then(manifest => {
                    if (manifest.name && manifest.version && manifest.manifest_version) {
                        addTestResult('Manifest Validation', true, `Valid manifest v${manifest.version}`);
                        
                        // Check if authentication URLs are removed
                        const hostPermissions = manifest.host_permissions || [];
                        const hasAuthUrl = hostPermissions.some(url => url.includes('tv-hub.org'));
                        if (hasAuthUrl) {
                            addTestResult('Authentication Removal', false, 'tv-hub.org still found in host_permissions');
                        } else {
                            addTestResult('Authentication Removal', true, 'All authentication URLs removed from manifest');
                        }
                    } else {
                        addTestResult('Manifest Validation', false, 'Invalid manifest structure');
                    }
                })
                .catch(e => {
                    addTestResult('Manifest Validation', false, `Error loading manifest: ${e.message}`);
                });
        }

        // Test 3: Check if popup.js loads without syntax errors
        function testPopupJS() {
            const script = document.createElement('script');
            script.onload = function() {
                addTestResult('JavaScript Load', true, 'popup.js loaded without syntax errors');
            };
            script.onerror = function() {
                addTestResult('JavaScript Load', false, 'popup.js failed to load or has syntax errors');
            };
            script.src = 'popup.js';
            document.head.appendChild(script);
        }

        // Test 4: Check if content.js loads
        function testContentJS() {
            fetch('content.js')
                .then(response => response.text())
                .then(content => {
                    // Check if authentication code is removed
                    const hasAuthCode = content.includes('tv-hub.org') || content.includes('CheckUser') || content.includes('accessKey');
                    if (hasAuthCode) {
                        addTestResult('Content.js Auth Removal', false, 'Authentication code still found in content.js');
                    } else {
                        addTestResult('Content.js Auth Removal', true, 'Authentication code removed from content.js');
                    }
                })
                .catch(e => {
                    addTestResult('Content.js Load', false, `Error loading content.js: ${e.message}`);
                });
        }

        // Test 5: Check whats-new.html
        function testWhatsNew() {
            fetch('whats-new.html')
                .then(response => response.text())
                .then(content => {
                    const hasLicensingInfo = content.includes('Bybit') || content.includes('subscription') || content.includes('API key');
                    if (hasLicensingInfo) {
                        addTestResult('Whats-new Licensing Removal', false, 'Licensing information still found in whats-new.html');
                    } else {
                        addTestResult('Whats-new Licensing Removal', true, 'Licensing information removed from whats-new.html');
                    }
                })
                .catch(e => {
                    addTestResult('Whats-new Load', false, `Error loading whats-new.html: ${e.message}`);
                });
        }

        // Test 6: Check for debugging console logs
        function testDebugging() {
            // Override console.log to capture debug messages
            const originalLog = console.log;
            const debugMessages = [];
            console.log = function(...args) {
                debugMessages.push(args.join(' '));
                originalLog.apply(console, arguments);
            };

            setTimeout(() => {
                console.log = originalLog; // Restore original console.log
                if (debugMessages.length > 0) {
                    addTestResult('Debug Logging', true, `Found ${debugMessages.length} debug messages - check browser console for details`);
                } else {
                    addTestResult('Debug Logging', true, 'No debug messages captured (this is normal)');
                }
            }, 3000);
        }

        // Run all tests
        window.onload = function() {
            addTestResult('Test Suite', true, 'Starting authentication removal tests...');

            testManifest();
            testPopupJS();
            testContentJS();
            testWhatsNew();
            testPopupLoad();
            testDebugging();

            // Add instructions for manual testing
            setTimeout(() => {
                const resultsDiv = document.getElementById('test-results');
                const instructionsDiv = document.createElement('div');
                instructionsDiv.className = 'test-result';
                instructionsDiv.style.backgroundColor = '#e7f3ff';
                instructionsDiv.style.border = '1px solid #b3d9ff';
                instructionsDiv.style.color = '#0066cc';
                instructionsDiv.innerHTML = `
                    <strong>Manual Testing Instructions:</strong><br>
                    1. Load the extension in Chrome (chrome://extensions/)<br>
                    2. Go to TradingView and open a chart with a strategy<br>
                    3. Click the extension icon<br>
                    4. Fill in: Pairs (e.g., BTCUSD), Timeframes (e.g., 1h), and at least one parameter<br>
                    5. For parameters: Field # (e.g., 1), Step Size (e.g., 1), From (e.g., 10), To (e.g., 20)<br>
                    6. Click "Find best settings" - check browser console (F12) for debug messages<br>
                    7. Try adding a second parameter with the + button
                `;
                resultsDiv.appendChild(instructionsDiv);
            }, 1000);
        };
    </script>
</body>
</html>
